# 构建阶段 - ARM版本
FROM --platform=linux/arm64 node:20-alpine AS builder

WORKDIR /app

# 复制依赖文件
COPY package.json package-lock.json* ./

# 安装依赖
RUN npm ci

# 复制源代码
COPY . .

# 构建应用
RUN npm run build

# 生产阶段
FROM --platform=linux/arm64 node:20-alpine AS runner

WORKDIR /app

ENV NODE_ENV=production

# 创建非root用户
RUN addgroup --system --gid 1001 nodejs && \
    adduser --system --uid 1001 nextjs

# 只复制生产必要的文件
COPY --from=builder /app/next.config.js ./
COPY --from=builder /app/public ./public
COPY --from=builder /app/.next/standalone ./
COPY --from=builder /app/.next/static ./.next/static

# 设置正确的权限
RUN chown -R nextjs:nodejs /app

# 使用非root用户运行
USER nextjs

EXPOSE 3000

# 修正ENV语法为新格式
ENV PORT=3000
ENV HOSTNAME="0.0.0.0"

# ARM特定优化
ENV NODE_OPTIONS="--max-old-space-size=2048"

# 启动应用
CMD ["node", "server.js"] 