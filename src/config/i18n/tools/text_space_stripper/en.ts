export const textSpaceStripperEn = {
  title: 'Text Space Stripper',
  description: 'Remove spaces and line breaks from text with various modes',
  input_text: 'Input Text',
  input_placeholder: 'Enter or paste text to process here...',
  output_text: 'Output Result',
  output_placeholder: 'Processed text will appear here...',
  tool_options: 'Processing Options',
  strip_mode: 'Strip Mode',
  strip_mode_both: 'Both Sides',
  strip_mode_start: 'Leading Spaces',
  strip_mode_end: 'Trailing Spaces',
  strip_mode_all: 'All Spaces',
  strip_mode_newlines: 'All Line Breaks',
  strip_mode_all_and_newlines: 'All Spaces and Line Breaks',
  copy_result: 'Copy Result',
  copied: 'Copied',
  copy_failed: 'Copy failed',
  clear: 'Clear',
  process: 'Process',
  load_example: 'Load Example'
};

export default textSpaceStripperEn; 