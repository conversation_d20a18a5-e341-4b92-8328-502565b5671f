export const textSpaceStripperZh = {
  title: '文本去空格换行',
  description: '去除文本中的空格和换行符，支持多种模式',
  input_text: '输入文本',
  input_placeholder: '在此输入或粘贴需要处理的文本...',
  output_text: '输出结果',
  output_placeholder: '处理后的文本将显示在这里...',
  tool_options: '处理选项',
  strip_mode: '去除方式',
  strip_mode_both: '两边空格',
  strip_mode_start: '左边空格',
  strip_mode_end: '右边空格',
  strip_mode_all: '全部空格',
  strip_mode_newlines: '所有换行符',
  strip_mode_all_and_newlines: '所有空格和换行符',
  copy_result: '复制结果',
  copied: '已复制',
  copy_failed: '复制失败',
  clear: '清空',
  process: '处理',
  load_example: '加载示例'
};

export default textSpaceStripperZh; 