export const imageCompressorZh = {
  title: '图片压缩',
  description: '纯前端实现的图片压缩工具，支持多种格式',
  select_image: '选择图片文件',
  choose_file: '选择文件',
  compression_settings: '压缩设置',
  quality: '质量',
  max_width: '最大宽度 (像素)',
  max_height: '最大高度 (像素)',
  output_format: '输出格式',
  auto_format: '自动 (保持原格式)',
  maintain_ratio: '保持宽高比',
  compression_info: '压缩信息',
  original_size: '原始大小',
  compressed_size: '压缩后大小',
  compression_ratio: '压缩率',
  compress_image: '压缩图片',
  compressing: '压缩中...',
  download_compressed: '下载压缩图片',
  reset: '重置',
  original_image: '原始图片',
  compressed_image: '压缩后图片',
  no_image_selected: '未选择图片',
  no_compressed_image: '未压缩图片',
  compression_failed: '压缩失败',
  bytes: '字节',
  kb: 'KB',
  mb: 'MB',
  gb: 'GB',
  usage_guide: '使用说明',
  guide_1: '支持多种图片格式的压缩，包括JPEG、PNG和WebP',
  guide_2: '可以调整压缩质量和最大尺寸，平衡文件大小和图片质量',
  guide_3: '支持保持原始宽高比，避免图片变形',
  guide_4: '压缩过程在浏览器中完成，不会上传图片到服务器',
  guide_5: '可以预览压缩前后的图片效果和文件大小变化',
  guide_6: '支持批量处理多张图片',
  guide_7: '压缩后的图片可以直接下载使用'
};

export default imageCompressorZh; 