export const imageCompressorEn = {
  title: 'Image Compressor',
  description: 'Pure frontend image compression tool supporting multiple formats',
  select_image: 'Select Image File',
  choose_file: 'Choose File',
  compression_settings: 'Compression Settings',
  quality: 'Quality',
  max_width: 'Max Width (pixels)',
  max_height: 'Max Height (pixels)',
  output_format: 'Output Format',
  auto_format: 'Auto (Keep Original Format)',
  maintain_ratio: 'Maintain Aspect Ratio',
  compression_info: 'Compression Info',
  original_size: 'Original Size',
  compressed_size: 'Compressed Size',
  compression_ratio: 'Compression Ratio',
  compress_image: 'Compress Image',
  compressing: 'Compressing...',
  download_compressed: 'Download Compressed Image',
  reset: 'Reset',
  original_image: 'Original Image',
  compressed_image: 'Compressed Image',
  no_image_selected: 'No Image Selected',
  no_compressed_image: 'No Compressed Image',
  compression_failed: 'Compression Failed',
  bytes: 'Bytes',
  kb: 'KB',
  mb: 'MB',
  gb: 'GB',
  usage_guide: 'Usage Guide',
  guide_1: 'Supports compression of multiple image formats, including JPEG, PNG, and WebP',
  guide_2: 'Adjust compression quality and maximum dimensions to balance file size and image quality',
  guide_3: 'Maintains original aspect ratio to prevent image distortion',
  guide_4: 'Compression is performed in the browser, no images are uploaded to servers',
  guide_5: 'Preview images before and after compression to see the effect and file size changes',
  guide_6: 'Supports batch processing of multiple images',
  guide_7: 'Compressed images can be downloaded directly for use'
};

export default imageCompressorEn; 