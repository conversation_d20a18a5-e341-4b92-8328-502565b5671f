export const cronGeneratorZh = {
  title: 'Cron表达式生成器',
  description: 'Linux/Unix Cron表达式生成与解析工具，支持查看最近10次执行时间',
  cron_expression: 'Cron表达式',
  generate: '生成表达式',
  parse: '解析表达式',
  next_executions: '未来执行时间',
  next_executions_count: '显示未来执行次数',
  custom_expression: '自定义表达式',
  expression_preview: '表达式预览',
  copy_expression: '复制表达式',
  copy_success: '复制成功',
  copy_failed: '复制失败',
  invalid_expression: '无效的Cron表达式',
  second: '秒',
  minute: '分',
  hour: '时',
  day: '日',
  month: '月',
  week: '周',
  year: '年',
  every: '每',
  specific: '指定',
  range: '范围',
  interval: '间隔',
  select_all: '全选',
  not_specified: '不指定',
  execution_times: '执行时间列表',
  from: '从',
  to: '到',
  step: '步长',
  presets: '常用表达式',
  every_minute: '每分钟',
  every_hour: '每小时',
  every_day_midnight: '每天零点',
  every_day_morning: '每天早上8点',
  every_monday: '每周一上午9点',
  every_month_first: '每月1号凌晨'
};

export default cronGeneratorZh; 