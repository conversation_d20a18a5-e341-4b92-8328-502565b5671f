export const cronGeneratorEn = {
  title: 'Cron Expression Generator',
  description: 'Linux/Unix Cron expression generator and parser with next 10 execution times preview',
  cron_expression: 'Cron Expression',
  generate: 'Generate Expression',
  parse: 'Parse Expression',
  next_executions: 'Next Execution Times',
  next_executions_count: 'Show Future Executions',
  custom_expression: 'Custom Expression',
  expression_preview: 'Expression Preview',
  copy_expression: 'Copy Expression',
  copy_success: 'Copied',
  copy_failed: 'Copy failed',
  invalid_expression: 'Invalid Cron Expression',
  second: 'Second',
  minute: 'Minute',
  hour: 'Hour',
  day: 'Day',
  month: 'Month',
  week: 'Week',
  year: 'Year',
  every: 'Every',
  specific: 'Specific',
  range: 'Range',
  interval: 'Interval',
  select_all: 'Select All',
  not_specified: 'Not Specified',
  execution_times: 'Execution Times',
  from: 'From',
  to: 'To',
  step: 'Step',
  presets: 'Common Expressions',
  every_minute: 'Every Minute',
  every_hour: 'Every Hour',
  every_day_midnight: 'Every Day at Midnight',
  every_day_morning: 'Every Day at 8 AM',
  every_monday: 'Every Monday at 9 AM',
  every_month_first: 'First Day of Month at Midnight'
};

export default cronGeneratorEn; 