export const timezoneConverterEn = {
  title: 'Timezone Converter',
  description: 'Convert time between different timezones',
  date_time: 'Date & Time',
  source_timezone: 'Source Timezone',
  target_timezone: 'Target Timezone',
  conversion_result: 'Conversion Result',
  converted_time: 'Converted Time',
  detailed_result: 'Detailed Result',
  current_time: 'Current Time',
  use_current_time: 'Use Current Time',
  copy: 'Copy',
  copied: 'Copied',
  copy_failed: '<PERSON>py failed',
  timezone_conversion_error: 'Timezone conversion error',
  input_date_time_select_timezone: 'Please enter date & time and select timezone to convert',
  timezone_note: 'Note: Timezone conversion results have taken daylight saving time into account, different regions may implement DST on different dates',
  timezone_display_note: 'Timezone names may vary based on your operating system and browser language settings',
  common_timezone_info: 'Common Timezone Information',
  asia_pacific: 'Asia Pacific',
  europe: 'Europe',
  americas: 'Americas',
  china: 'China',
  japan: 'Japan',
  korea: 'Korea',
  singapore: 'Singapore',
  india: 'India',
  australia: 'Australia',
  uk: 'UK',
  france: 'France',
  germany: 'Germany',
  russia: 'Russia',
  us_eastern: 'US Eastern',
  us_central: 'US Central',
  us_western: 'US Western',
  brazil: 'Brazil',
  about_timezone: 'About Timezone Notation:',
  timezone_offset_info: 'Offset like "+08:00" means 8 hours ahead of UTC',
  timezone_dst_info: 'Format like "+01:00/+02:00" indicates standard time/daylight saving time',
  dst_implementation: 'DST is typically implemented from March to November in the Northern Hemisphere',
  source_time: 'Source Time',
  target_time: 'Target Time',
  timestamp: 'Timestamp',
  iso_format: 'ISO Format',
  invalid_date_time: 'Invalid date time',
  please_enter_valid_date_time: 'Please enter a valid date time'
};

export default timezoneConverterEn; 