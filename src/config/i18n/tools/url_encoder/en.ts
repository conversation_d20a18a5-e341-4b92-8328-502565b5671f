export const urlEncoderEn = {
  title: 'URL Encoder/Decoder',
  description: 'Encode and decode URL special characters',
  original_text: 'Original Text',
  url_encoded_text: 'URL Encoded Text',
  input_original_text: 'Input Original Text',
  input_encoded_text: 'Input Encoded Text',
  enter_text_to_encode: 'Enter text to encode here...',
  enter_text_to_decode: 'Enter URL encoded text to decode here...',
  encoding_result: 'Encoding Result',
  decoding_result: 'Decoding Result',
  encoded_result_placeholder: 'Encoding result will appear here...',
  decoded_result_placeholder: 'Decoding result will appear here...',
  clear: 'Clear',
  show_history: 'Show History',
  hide_history: 'Hide History',
  copied: 'Copied',
  copy_result: 'Copy Result',
  error_processing: 'Error processing text',
  copy_failed: 'Failed to copy to clipboard',
  copied_to_clipboard: 'Copied to clipboard',
  history: 'History',
  clear_history: 'Clear History',
  no_history: 'No history records',
  encode: 'Encode',
  decode: 'Decode',
  uri_encoding: 'URI Encoding',
  uri_component_encoding: 'URI Component Encoding',
  uri_encoding_description: 'Uses encodeURI and decodeURI functions - does not encode URL necessary characters like / ? : @ & = + $ #',
  uri_component_description: 'Uses encodeURIComponent and decodeURIComponent functions - encodes all special characters including / ? : @ & = + $ #',
  url_encoding_explanation: 'URL Encoding Explanation',
  uri_vs_component: 'URI Encoding vs URI Component Encoding',
  encode_uri_description: 'Used to encode entire URLs, preserves URL special characters',
  encode_uri_component_description: 'Used to encode URL parameter values, encodes all special characters',
  usage_scenarios: 'Usage Scenarios',
  scenario_1: 'Pass non-ASCII characters, such as Chinese or special characters, in URLs',
  scenario_2: 'Prevent URL injection and XSS attacks',
  scenario_3: 'Ensure correct URL format, avoiding parsing errors caused by special characters',
  encoding_rules: 'Encoding Rules',
  rules_1: 'Only letters, numbers and the following special characters remain unchanged: - _ . ! ~ * \' ( )',
  rules_2: 'Other characters are encoded as % followed by two hexadecimal digits'
};

export default urlEncoderEn; 