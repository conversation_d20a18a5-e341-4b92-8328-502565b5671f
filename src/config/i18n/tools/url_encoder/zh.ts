export const urlEncoderZh = {
  title: 'URL编解码',
  description: 'URL特殊字符的编码与解码转换',
  original_text: '原始文本',
  url_encoded_text: 'URL编码文本',
  input_original_text: '输入原始文本',
  input_encoded_text: '输入编码文本',
  enter_text_to_encode: '在此输入要编码的文本...',
  enter_text_to_decode: '在此输入要解码的URL编码...',
  encoding_result: '编码结果',
  decoding_result: '解码结果',
  encoded_result_placeholder: '编码结果将显示在这里...',
  decoded_result_placeholder: '解码结果将显示在这里...',
  clear: '清空',
  show_history: '显示历史记录',
  hide_history: '隐藏历史记录',
  copied: '已复制',
  copy_result: '复制结果',
  error_processing: '处理文本时发生错误',
  copy_failed: '复制到剪贴板失败',
  copied_to_clipboard: '已复制到剪贴板',
  history: '历史记录',
  clear_history: '清空历史',
  no_history: '暂无历史记录',
  encode: '编码',
  decode: '解码',
  uri_encoding: 'URI编码',
  uri_component_encoding: 'URI组件编码',
  uri_encoding_description: '使用encodeURI和decodeURI函数 - 不编码URL必要字符如 / ? : @ & = + $ #',
  uri_component_description: '使用encodeURIComponent和decodeURIComponent函数 - 编码所有特殊字符包括 / ? : @ & = + $ #',
  url_encoding_explanation: 'URL编码说明',
  uri_vs_component: 'URI编码 vs URI组件编码',
  encode_uri_description: '用于编码整个URL，保留URL特殊字符',
  encode_uri_component_description: '用于编码URL参数值，会编码所有特殊字符',
  usage_scenarios: '应用场景',
  scenario_1: '在URL中传递中文、特殊字符或其他非ASCII字符',
  scenario_2: '防止URL注入和XSS攻击',
  scenario_3: '确保URL格式正确，避免因特殊字符导致的解析错误',
  encoding_rules: '编码规则',
  rules_1: '仅字母、数字和以下特殊符号保持不变: - _ . ! ~ * \' ( )',
  rules_2: '其他字符会被编码为 % 后跟两位十六进制数字'
};

export default urlEncoderZh; 