export const base64ToImageEn = {
  title: 'Base64 Image Converter',
  description: 'Convert between Base64 string and image',
  
  // Base64 to Image
  input_title: 'Input Base64 String',
  output_title: 'Image Output',
  base64_input: 'Base64 String',
  input_placeholder: 'Paste your Base64 encoded image string here...',
  load_example: 'Load Example',
  clear_all: 'Clear',
  copy: 'Copy Base64',
  download_image: 'Download Image',
  converted_image: 'Converted Image',
  no_image: 'No image to display yet. Paste a Base64 string to convert.',
  processing_error: 'Error processing Base64 string',
  invalid_data_url: 'Invalid data URL format',
  invalid_base64: 'Invalid Base64 encoded string',
  copy_failed: 'Failed to copy to clipboard',
  image_type: 'Image Type',
  file_name: 'File Name',
  
  // Image to Base64
  image_input_title: 'Input Image',
  base64_output_title: 'Base64 Output',
  select_image: 'Select Image File',
  choose_file: 'Choose File',
  uploaded_image: 'Uploaded Image',
  no_uploaded_image: 'No image uploaded yet. Select an image file to convert.',
  output_placeholder: 'Base64 output will be displayed here...',
  include_prefix: 'Include data URL prefix (data:image/type;base64,)',
  copy_base64: 'Copy Base64',
  save_as_text: 'Save as Text File',
  invalid_image_file: 'Invalid image file. Please select a valid image.',
  file_too_large: 'File is too large. Maximum size is 10MB.',
  file_reading_error: 'Error reading file',
  conversion_success: 'Image successfully converted to Base64',
  
  // Tabs and mode switching
  base64_to_image_tab: 'Base64 to Image',
  image_to_base64_tab: 'Image to Base64',
  switch_to_image_to_base64: 'Switch to Image to Base64',
  switch_to_base64_to_image: 'Switch to Base64 to Image',
  
  // Guides
  usage_guide: 'Usage Guide',
  guide_1: 'Paste a Base64 encoded string to convert it to an image',
  guide_2: 'Supports both pure Base64 and data URL formats (data:image/png;base64,...)',
  guide_3: 'The tool will attempt to detect the image format automatically',
  guide_4: 'You can download the converted image with your preferred file name',
  guide_5: 'Upload an image file to convert it to Base64 string',
  guide_6: 'Choose whether to include the data URL prefix or only the raw Base64 data',
  guide_7: 'The generated Base64 can be copied to clipboard or saved as a text file',
  guide_8: 'Supported image formats include JPG, PNG, GIF, BMP, WebP, and SVG'
}; 