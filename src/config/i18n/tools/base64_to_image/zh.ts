export const base64ToImageZh = {
  title: 'Base64图片转换器',
  description: 'Base64字符串与图片互相转换',
  
  // Base64转图片
  input_title: '输入Base64字符串',
  output_title: '图片输出',
  base64_input: 'Base64字符串',
  input_placeholder: '在此粘贴您的Base64编码图片字符串...',
  load_example: '加载示例',
  clear_all: '清空',
  copy: '复制Base64',
  download_image: '下载图片',
  converted_image: '转换后的图片',
  no_image: '暂无图片显示。粘贴Base64字符串进行转换。',
  processing_error: '处理Base64字符串时出错',
  invalid_data_url: '无效的数据URL格式',
  invalid_base64: '无效的Base64编码字符串',
  copy_failed: '复制到剪贴板失败',
  image_type: '图片类型',
  file_name: '文件名',
  
  // 图片转Base64
  image_input_title: '输入图片',
  base64_output_title: 'Base64输出',
  select_image: '选择图片文件',
  choose_file: '选择文件',
  uploaded_image: '已上传图片',
  no_uploaded_image: '暂无已上传图片。选择图片文件进行转换。',
  output_placeholder: 'Base64输出将显示在这里...',
  include_prefix: '包含数据URL前缀（data:image/type;base64,）',
  copy_base64: '复制Base64',
  save_as_text: '保存为文本文件',
  invalid_image_file: '无效的图片文件。请选择有效的图片。',
  file_too_large: '文件太大。最大大小为10MB。',
  file_reading_error: '读取文件出错',
  conversion_success: '图片已成功转换为Base64',
  
  // 选项卡和模式切换
  base64_to_image_tab: 'Base64转图片',
  image_to_base64_tab: '图片转Base64',
  switch_to_image_to_base64: '切换到图片转Base64',
  switch_to_base64_to_image: '切换到Base64转图片',
  
  // 使用指南
  usage_guide: '使用指南',
  guide_1: '粘贴Base64编码字符串将其转换为图片',
  guide_2: '支持纯Base64和数据URL格式（data:image/png;base64,...）',
  guide_3: '工具会自动尝试检测图片格式',
  guide_4: '可以使用您喜欢的文件名下载转换后的图片',
  guide_5: '上传图片文件将其转换为Base64字符串',
  guide_6: '选择是否包含数据URL前缀或仅保留原始Base64数据',
  guide_7: '生成的Base64可以复制到剪贴板或保存为文本文件',
  guide_8: '支持的图片格式包括JPG、PNG、GIF、BMP、WebP和SVG'
}; 