export const textCounterZh = {
  title: '文本字数统计',
  description: '统计文本的字符数、词数、句子数和段落数',
  input_text: '输入文本',
  input_placeholder: '在此输入或粘贴文本...',
  empty_notice: '输入文本后将在左侧显示统计结果',
  statistics_results: '统计结果',
  copy_results: '复制结果',
  copied: '已复制',
  tool_options: '工具选项',
  load_chinese_example: '加载中文示例',
  load_english_example: '加载英文示例',
  clear: '清空',
  copy_failed: '复制失败',
  statistics: {
    total_characters: '总字符数',
    characters_no_spaces: '不含空格字符数',
    chinese_characters: '中文字符数',
    total_words: '总词数',
    chinese_words: '中文词数',
    english_words: '英文词数',
    sentences: '句子数',
    paragraphs: '段落数',
    lines: '行数'
  },
  copy_result_text: '字符统计结果:\n总字符数: {characters}\n不含空格字符数: {charactersNoSpaces}\n中文字符数: {chineseCharacters}\n总词数: {words}\n中文词数: {chineseWords}\n英文词数: {englishWords}\n句子数: {sentences}\n段落数: {paragraphs}\n行数: {lines}'
};

export default textCounterZh; 