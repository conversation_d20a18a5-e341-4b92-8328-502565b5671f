export const textCounterEn = {
  title: 'Text Counter',
  description: 'Count characters, words, sentences, and paragraphs in text',
  input_text: 'Input Text',
  input_placeholder: 'Enter or paste text here...',
  empty_notice: 'Statistics will be displayed on the left once text is entered',
  statistics_results: 'Statistics Results',
  copy_results: 'Copy Results',
  copied: 'Copied',
  tool_options: 'Tool Options',
  load_chinese_example: 'Load Chinese Example',
  load_english_example: 'Load English Example',
  clear: 'Clear',
  copy_failed: 'Copy failed',
  statistics: {
    total_characters: 'Total Characters',
    characters_no_spaces: 'Characters (No Spaces)',
    chinese_characters: 'Chinese Characters',
    total_words: 'Total Words',
    chinese_words: 'Chinese Words',
    english_words: 'English Words',
    sentences: 'Sentences',
    paragraphs: 'Paragraphs',
    lines: 'Lines'
  },
  copy_result_text: 'Text Statistics Results:\nTotal Characters: {characters}\nCharacters (No Spaces): {charactersNoSpaces}\nChinese Characters: {chineseCharacters}\nTotal Words: {words}\nChinese Words: {chineseWords}\nEnglish Words: {englishWords}\nSentences: {sentences}\nParagraphs: {paragraphs}\nLines: {lines}'
};

export default textCounterEn; 