export const qrcodeGeneratorZh = {
  title: '二维码生成',
  description: '文本/URL转二维码图片工具',
  basic_settings: '基本设置',
  qrcode_content: '二维码内容',
  size_pixels: '尺寸（像素）',
  margin_pixels: '边距（像素）',
  dot_style: '点样式',
  squares: '方块',
  dots: '圆点',
  color_settings: '颜色设置',
  background_color: '背景颜色',
  foreground_color: '前景颜色',
  eye_color: '眼睛颜色',
  eye_radius: '眼睛圆角',
  preset_colors: '预设颜色',
  preset_classic_bw: '黑白经典',
  preset_blue_white: '蓝白商务',
  preset_vibrant_red: '活力红',
  preset_fresh_green: '清新绿',
  preset_tech_purple: '科技紫',
  preset_dark_mode: '暗黑模式',
  preset_orange_accent: '橙色点缀',
  preset_warm_brown: '温暖棕',
  logo_settings: 'Logo设置',
  upload_logo: '上传Logo',
  remove: '移除',
  logo_width: 'Logo宽度（像素）',
  logo_height: 'Logo高度（像素）',
  logo_opacity: 'Logo透明度',
  remove_code_behind_logo: '移除Logo后方的二维码',
  preview: '预览',
  copy_content: '复制内容',
  download_qrcode: '下载二维码',
  reset_settings: '重置设置',
  please_input_content: '请输入内容生成二维码',
  instructions: '使用说明',
  instruction_1: '1. 输入需要生成二维码的内容，支持网址、文本等',
  instruction_2: '2. 通过右侧控制面板调整二维码的样式、颜色等参数',
  instruction_3: '3. 可以上传Logo图片并设置其大小和透明度',
  instruction_4: '4. 点击下载按钮保存生成的二维码图片',
  note: '注意：内容过长可能影响二维码识别率，建议使用简短链接',
  input_placeholder: '输入网址、文本或其他内容...',
  copy_failed: '复制失败',
  download_failed: '下载失败'
};

export default qrcodeGeneratorZh; 