export const qrcodeGeneratorEn = {
  title: 'QR Code Generator',
  description: 'Convert text/URL to QR code image',
  basic_settings: 'Basic Settings',
  qrcode_content: 'QR Code Content',
  size_pixels: 'Size (pixels)',
  margin_pixels: 'Margin (pixels)',
  dot_style: 'Dot Style',
  squares: 'Squares',
  dots: 'Dots',
  color_settings: 'Color Settings',
  background_color: 'Background Color',
  foreground_color: 'Foreground Color',
  eye_color: 'Eye Color',
  eye_radius: 'Eye Radius',
  preset_colors: 'Preset Colors',
  preset_classic_bw: 'Classic B&W',
  preset_blue_white: 'Business Blue',
  preset_vibrant_red: 'Vibrant Red',
  preset_fresh_green: 'Fresh Green',
  preset_tech_purple: 'Tech Purple',
  preset_dark_mode: 'Dark Mode',
  preset_orange_accent: 'Orange Accent',
  preset_warm_brown: 'Warm Brown',
  logo_settings: 'Logo Settings',
  upload_logo: 'Upload Logo',
  remove: 'Remove',
  logo_width: 'Logo Width (pixels)',
  logo_height: 'Logo Height (pixels)',
  logo_opacity: 'Logo Opacity',
  remove_code_behind_logo: 'Remove QR Code Behind Logo',
  preview: 'Preview',
  copy_content: 'Copy Content',
  download_qrcode: 'Download QR Code',
  reset_settings: 'Reset Settings',
  please_input_content: 'Please input content to generate QR code',
  instructions: 'Instructions',
  instruction_1: '1. Enter the content you want to generate as a QR code (URL, text, etc.)',
  instruction_2: '2. Adjust QR code style, color and other parameters in the control panel',
  instruction_3: '3. You can upload a logo image and set its size and opacity',
  instruction_4: '4. Click the download button to save the generated QR code image',
  note: 'Note: Long content may affect QR code readability, consider using short links',
  input_placeholder: 'Enter URL, text or other content...',
  copy_failed: 'Copy failed',
  download_failed: 'Download failed'
};

export default qrcodeGeneratorEn; 