export const timestampConverterEn = {
  title: 'Timestamp Converter',
  description: 'Unix timestamp and date/time conversion tool',
  timestamp: 'Timestamp',
  datetime: 'Date & Time',
  current_time: 'Current Time',
  current_time_colon: 'Current Time:',
  today_zero: 'Today at 00:00',
  this_monday: 'This Monday',
  this_month_start: 'This Month Start',
  this_year_start: 'This Year Start',
  enter_unix_timestamp: 'Enter Unix timestamp',
  enter_datetime: 'Enter date & time (e.g. 2099-12-31 23:59:59)',
  use_current_time: 'Use Current Time',
  common_timestamps: 'Common Timestamps',
  copy_timestamp: 'Copy Timestamp',
  copy_datetime: 'Copy Date & Time',
  swap_positions: 'Swap Positions',
  timestamp_conversion_error: 'Timestamp conversion error',
  datetime_conversion_error: 'Date & time conversion error',
  datetime_format_error: 'Date & time format error',
  copy_failed: 'Copy failed'
};

export default timestampConverterEn; 