export const htmlMarkdownConverterEn = {
  title: 'HTML & Markdown Converter',
  description: 'Convert between HTML and Markdown formats',
  md2html: 'Markdown → HTML',
  html2md: 'HTML → Markdown',
  load_example: 'Load Example',
  clear: 'Clear',
  loading_modules: 'Loading conversion modules, please wait...',
  md_input: 'Markdown Input',
  html_input: 'HTML Input',
  md_output: 'Markdown Output',
  html_output: 'HTML Output',
  exchange: 'Exchange Input/Output',
  convert: 'Convert',
  converting: 'Converting...',
  md_placeholder: 'Enter Markdown content...',
  html_placeholder: 'Enter HTML content...',
  md_result_placeholder: 'Markdown conversion result will appear here...',
  html_result_placeholder: 'HTML conversion result will appear here...',
  copy_result: 'Copy Result',
  copied: 'Copied',
  error_empty: 'Please enter content to convert',
  error_convert: 'Conversion failed: {error}',
  error_unknown: 'Unknown error',
  error_copy: 'Failed to copy to clipboard',
  error_load: 'Failed to load conversion libraries, please refresh the page',
  feature_title: 'Features',
  feature_intro: 'This tool provides bidirectional conversion between HTML and Markdown, supporting the following features:',
  feature_1: 'Complete Markdown syntax support, including headings, lists, code blocks, tables, etc.',
  feature_2: 'Preserves HTML formatting and styles',
  feature_3: 'Supports nested structures, such as nested lists and quotes',
  feature_4: 'Maintains clear document structure after conversion',
  md2html_description: 'Use this tool to convert Markdown documents into HTML code for web display.',
  html2md_description: 'Use this tool to convert HTML code into more readable and writable Markdown format.'
};

export default htmlMarkdownConverterEn; 