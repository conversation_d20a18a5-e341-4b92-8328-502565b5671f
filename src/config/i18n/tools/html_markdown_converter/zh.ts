export const htmlMarkdownConverterZh = {
  title: 'HTML与Markdown互转',
  description: '在HTML和Markdown格式之间进行双向转换',
  md2html: 'Markdown → HTML',
  html2md: 'HTML → Markdown',
  load_example: '加载示例',
  clear: '清空',
  loading_modules: '正在加载转换模块，请稍候...',
  md_input: 'Markdown 输入',
  html_input: 'HTML 输入',
  md_output: 'Markdown 输出',
  html_output: 'HTML 输出',
  exchange: '交换输入输出',
  convert: '转换',
  converting: '转换中...',
  md_placeholder: '请输入Markdown内容...',
  html_placeholder: '请输入HTML内容...',
  md_result_placeholder: 'Markdown 转换结果将显示在这里...',
  html_result_placeholder: 'HTML 转换结果将显示在这里...',
  copy_result: '复制结果',
  copied: '已复制',
  error_empty: '请输入需要转换的内容',
  error_convert: '转换失败: {error}',
  error_unknown: '未知错误',
  error_copy: '复制到剪贴板失败',
  error_load: '加载转换库失败，请刷新页面重试',
  feature_title: '功能说明',
  feature_intro: '此工具提供HTML与Markdown之间的双向转换功能，支持以下特性：',
  feature_1: '完整的Markdown语法支持，包括标题、列表、代码块、表格等',
  feature_2: '保留HTML格式和样式',
  feature_3: '支持嵌套结构，如嵌套列表和引用',
  feature_4: '转换后保持文档结构清晰',
  md2html_description: '使用此工具可以将Markdown文档转换为可在网页中显示的HTML代码。',
  html2md_description: '使用此工具可以将HTML代码转换为更易读写的Markdown格式。'
};

export default htmlMarkdownConverterZh; 