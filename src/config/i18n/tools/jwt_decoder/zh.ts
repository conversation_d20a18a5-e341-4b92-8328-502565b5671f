export const jwtDecoderZh = {
  title: 'JWT解析与验证',
  description: '解码和验证JSON Web Token',
  decode_jwt: '解码JWT令牌',
  jwt_token: 'JWT令牌:',
  hide_parts: '隐藏分段',
  show_parts: '显示分段',
  paste_jwt: '粘贴JWT令牌到这里...',
  token_structure: 'JWT令牌结构',
  header: '头部',
  payload: '载荷',
  signature: '签名',
  parsing_error: '解析JWT令牌时发生错误',
  token_empty: 'JWT令牌不能为空',
  invalid_format: '无效的JWT格式，应为三段式结构（header.payload.signature）',
  parsing_failed: 'JWT解析失败，可能包含无效的JSON数据',
  invalid_base64: '无效的Base64编码',
  expired: '已过期',
  days: '天',
  hours: '小时',
  minutes: '分钟',
  seconds: '秒',
  copied_content: '内容已复制到剪贴板',
  copy_failed: '复制失败:',
  clipboard_error: '复制到剪贴板失败',
  token_copied: 'JWT令牌已复制到剪贴板',
  load_example: '加载示例',
  clear: '清空',
  copy_token: '复制令牌',
  jwt_status: 'JWT状态:',
  format_valid: '格式有效',
  format_invalid: '格式无效',
  expiration_status: '过期状态:',
  issue_time: '签发时间:',
  header_tab: '头部 (Header)',
  payload_tab: '载荷 (Payload)',
  signature_tab: '签名 (Signature)',
  copied: '已复制',
  copy: '复制',
  // 信息面板
  what_is_jwt: '什么是JWT？',
  jwt_full_name: 'JSON Web Token（JWT）解码与验证',
  jwt_intro: 'JWT（JSON Web Token）是一种开放标准，用于在网络应用间传递声明（Claims）的紧凑且自包含的方式。',
  jwt_parts: 'JWT由三部分组成，各部分用点（.）分隔：',
  header_desc: '包含令牌类型和签名算法',
  payload_desc: '包含声明（Claims），如用户信息、权限等',
  signature_desc: '用于验证消息的签名',
  tool_purpose: '使用此工具可以快速解码JWT令牌，验证其格式，并检查过期状态，但不会验证签名的有效性。',
  common_use_cases: '常见的JWT应用场景',
  use_case_1: '身份验证（Authentication）',
  use_case_2: '信息交换（Information Exchange）',
  use_case_3: '单点登录（Single Sign On）',
  use_case_4: '授权（Authorization）',
  use_case_5: 'API访问控制',
  // 过期状态
  valid_remaining: '有效，剩余 {time}',
  not_set: '未设置过期时间',
  // 示例JWT用户
  example_user: '小白工具箱用户'
};

export default jwtDecoderZh; 