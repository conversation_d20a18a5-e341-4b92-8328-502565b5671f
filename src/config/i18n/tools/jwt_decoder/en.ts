export const jwtDecoderEn = {
  title: 'JWT Decoder & Validator',
  description: 'Decode and verify JSON Web Tokens',
  decode_jwt: 'Decode JWT Token',
  jwt_token: 'JWT Token:',
  hide_parts: 'Hide Parts',
  show_parts: 'Show Parts',
  paste_jwt: 'Paste JWT token here...',
  token_structure: 'JWT Token Structure',
  header: 'Header',
  payload: 'Payload',
  signature: 'Signature',
  parsing_error: 'Error parsing JWT token',
  token_empty: 'JWT token cannot be empty',
  invalid_format: 'Invalid JWT format, should be a three-part structure (header.payload.signature)',
  parsing_failed: 'JWT parsing failed, may contain invalid JSON data',
  invalid_base64: 'Invalid Base64 encoding',
  expired: 'Expired',
  days: 'days',
  hours: 'hours',
  minutes: 'minutes',
  seconds: 'seconds',
  copied_content: 'Content copied to clipboard',
  copy_failed: 'Copy failed:',
  clipboard_error: 'Failed to copy to clipboard',
  token_copied: 'JWT token copied to clipboard',
  load_example: 'Load Example',
  clear: 'Clear',
  copy_token: 'Copy Token',
  jwt_status: 'JWT Status:',
  format_valid: 'Format Valid',
  format_invalid: 'Format Invalid',
  expiration_status: 'Expiration Status:',
  issue_time: 'Issue Time:',
  header_tab: 'Header',
  payload_tab: 'Payload',
  signature_tab: 'Signature',
  copied: 'Copied',
  copy: 'Copy',
  // Info panel
  what_is_jwt: 'What is JWT?',
  jwt_full_name: 'JSON Web Token (JWT) Decoder & Validator',
  jwt_intro: 'JWT (JSON Web Token) is an open standard used to securely transmit information between parties as a compact and self-contained object.',
  jwt_parts: 'JWT consists of three parts separated by dots (.):',
  header_desc: 'Contains the token type and signing algorithm',
  payload_desc: 'Contains claims, such as user info, permissions, etc.',
  signature_desc: 'Used to verify the message signature',
  tool_purpose: 'This tool allows you to quickly decode JWT tokens, verify their format, and check expiration status, but does not verify signature validity.',
  common_use_cases: 'Common JWT Use Cases',
  use_case_1: 'Authentication',
  use_case_2: 'Information Exchange',
  use_case_3: 'Single Sign On',
  use_case_4: 'Authorization',
  use_case_5: 'API Access Control',
  // Expiration status
  valid_remaining: 'Valid, remaining {time}',
  not_set: 'No expiration time set',
  // Example JWT user
  example_user: 'xiaoxia User'
};

export default jwtDecoderEn; 