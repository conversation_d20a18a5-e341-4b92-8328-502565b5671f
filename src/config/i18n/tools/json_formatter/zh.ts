export const jsonFormatterZh = {
  title: 'JSON格式化',
  description: 'JSON数据美化与验证工具',
  beautify: '美化',
  compress: '压缩',
  expand_all: '全部展开',
  collapse_all: '全部折叠',
  clear: '清空',
  load_example: '加载示例',
  history: '历史记录',
  save: '保存',
  cancel: '取消',
  input_json: '输入JSON',
  paste_json_here: '粘贴JSON到此处',
  jsonpath_query: 'JSONPath查询',
  optional: '(可选)',
  enter_jsonpath: '输入JSONPath表达式，例如: $.store.book[0].title',
  query_result: '查询结果',
  output: '格式化结果',
  copy: '复制',
  large_json_warning: '大文件模式',
  json_history: 'JSON历史记录',
  history_empty: '暂无历史记录',
  remove_favorite: '取消收藏',
  add_favorite: '添加收藏',
  edit_title: '编辑标题',
  delete: '删除',
  save_to_history: '保存到历史记录',
  enter_title: '输入标题',
  json_valid: '✓ JSON校验通过',
  json_invalid: '✗ 无效的JSON',
  large_json_processed: '✓ 大型JSON已处理 ({size} KB)',
  load_history_error: '加载历史记录失败:',
  copy_failed: '复制失败:',
  normal_mode: '普通模式',
  fold_mode: '折叠模式',
  reformat: '重新格式化',
  processing: '处理中...',
  cancel_processing: '取消处理',
  processing_large_json: '处理大型JSON中...',
  processing_large_json_message: '正在处理大型JSON...\n这可能需要几秒钟',
  parsing_json: '正在解析JSON...',
  characters: '字符',
  jsonpath_placeholder: '输入JSONPath查询，例如: author.name',
  query_result_placeholder: '查询结果将显示在这里',
  favorites: '收藏项目',
  all_history: '全部历史',
  no_saved_records: '暂无保存的JSON记录',
  save_first_record: '点击"保存"按钮添加您的第一条记录',
  edit_saved_json: '编辑保存的JSON',
  update: '更新',
  paste_json_placeholder: '请输入或粘贴JSON数据...',
  usage_guide: '使用说明',
  guide_1: '支持格式化和验证标准JSON数据',
  guide_2: '可以处理常见的不规范JSON格式(如JS对象)',
  guide_3: '支持折叠模式，方便查看复杂JSON结构',
  guide_4: 'JSONPath查询支持点符号(如author.name)和数组索引(如features[0])',
  guide_5: '展示压缩/美化模式可以自由切换',
  guide_6: '处理大型JSON数据可能需要几秒钟时间',
  guide_7: '可以保存多个JSON数据到历史记录中，方便比较查看',
  guide_8: '提供字符串转义和反转义功能，处理特殊字符',
  remove_slash: '移除斜杠',
  escape_string: '字符串转义',
  unescape_string: '字符串反转义'
};

export default jsonFormatterZh; 