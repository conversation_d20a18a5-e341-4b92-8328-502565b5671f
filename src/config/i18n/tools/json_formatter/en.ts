export const jsonFormatterEn = {
  title: 'JSON Formatter',
  description: 'JSON data beautify and validation tool',
  beautify: 'Beautify',
  compress: 'Compress',
  expand_all: 'Expand All',
  collapse_all: 'Collapse All',
  clear: 'Clear',
  load_example: 'Load Example',
  history: 'History',
  save: 'Save',
  cancel: 'Cancel',
  input_json: 'Input JSON',
  paste_json_here: 'Paste JSON here',
  jsonpath_query: 'JSONPath Query',
  optional: '(Optional)',
  enter_jsonpath: 'Enter JSONPath expression, e.g.: $.store.book[0].title',
  query_result: 'Query Result',
  output: 'Formatted Result',
  copy: 'Copy',
  large_json_warning: 'Large File Mode',
  json_history: 'JSON History',
  history_empty: 'No history records',
  remove_favorite: 'Remove Favorite',
  add_favorite: 'Add Favorite',
  edit_title: 'Edit Title',
  delete: 'Delete',
  save_to_history: 'Save to History',
  enter_title: 'Enter Title',
  json_valid: '✓ JSON is valid',
  json_invalid: '✗ Invalid JSON',
  large_json_processed: '✓ Large JSON processed ({size} KB)',
  load_history_error: 'Failed to load history:',
  copy_failed: 'Copy failed:',
  normal_mode: 'Normal Mode',
  fold_mode: 'Fold Mode',
  reformat: 'Reformat',
  processing: 'Processing...',
  cancel_processing: 'Cancel Processing',
  processing_large_json: 'Processing large JSON...',
  processing_large_json_message: 'Processing large JSON...\nThis may take a few seconds',
  parsing_json: 'Parsing JSON...',
  characters: 'characters',
  jsonpath_placeholder: 'Enter JSONPath query, e.g.: author.name',
  query_result_placeholder: 'Query results will be shown here',
  favorites: 'Favorites',
  all_history: 'All History',
  no_saved_records: 'No saved JSON records',
  save_first_record: 'Click the "Save" button to add your first record',
  edit_saved_json: 'Edit Saved JSON',
  update: 'Update',
  paste_json_placeholder: 'Please enter or paste JSON data...',
  usage_guide: 'Usage Guide',
  guide_1: 'Supports formatting and validating standard JSON data',
  guide_2: 'Can process common non-standard JSON formats (like JS objects)',
  guide_3: 'Supports fold mode for easy viewing of complex JSON structures',
  guide_4: 'JSONPath queries support dot notation (e.g., author.name) and array indices (e.g., features[0])',
  guide_5: 'Compress/Beautify mode can be freely switched',
  guide_6: 'Processing large JSON data may take a few seconds',
  guide_7: 'Multiple JSON data can be saved to history for comparison',
  guide_8: 'Provides string escape and unescape functions for special characters',
  remove_slash: 'Remove Slashes',
  escape_string: 'Escape String',
  unescape_string: 'Unescape String'
};

export default jsonFormatterEn; 