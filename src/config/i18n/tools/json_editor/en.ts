export const jsonEditorEn = {
  title: 'JSON Editor',
  description: 'Online JSON data editing and modification',
  editor_loading: 'Editor loading...',
  copy: 'Copy',
  download: 'Download',
  upload: 'Upload',
  clear: 'Clear',
  file_name: 'File name:',
  copied_to_clipboard: 'Copied to clipboard',
  copy_failed: 'Copy failed:',
  download_success: 'Downloaded {fileName}',
  download_failed: 'Download failed: Invalid JSON data',
  loaded_file: 'Loaded file {fileName}',
  loaded_as_text: 'Loaded {fileName} as text (invalid JSON format)',
  read_file_failed: 'Failed to read file',
  editor_cleared: 'Editor has been cleared',
  confirm_clear: 'Are you sure you want to clear the editor content?',
  invalid_json: 'Unable to get valid JSON content',
  edit_json_here: 'Edit your JSON data here'
};

export default jsonEditorEn; 