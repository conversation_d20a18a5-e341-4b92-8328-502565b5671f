export const regexTesterEn = {
  title: 'Regex Tester',
  description: 'Test regular expression matching in real-time',
  examples: {
    title: 'Common Examples',
    email: 'Email',
    phone: 'Phone Number',
    url: 'URL',
    ip: 'IP Address',
    chinese: 'Chinese Characters'
  },
  example_texts: {
    phone: 'Contact phone: 13812345678 or contact email: <EMAIL>',
    url: 'Visit https://example.com or http://localhost:3000',
    ip: 'Server IP: *********** and ******** and 256.256.256.256',
    chinese: 'Hello 你好，世界！World'
  },
  options: 'Regex Options',
  flags: 'Flags',
  flag_descriptions: {
    global: 'Global',
    case_insensitive: 'Case Insensitive',
    multiline: 'Multiline',
    dotall: 'Dot Matches All'
  },
  show_capture_groups: 'Show Capture Groups',
  regex_expression: 'Regular Expression',
  copy: 'Copy',
  enter_regex: 'Enter regular expression...',
  test_text: 'Test Text',
  character_count: 'Character Count',
  enter_test_text: 'Enter text to test...',
  match_results: 'Match Results',
  match_count: 'Match Count',
  found: 'Found',
  matches: 'matches',
  original_text_length: 'Original Text Length',
  result_characters: 'characters',
  no_matches: 'No Matches Found',
  capture_groups: 'Capture Group Details',
  match: 'Match'
};

export default regexTesterEn; 