export const colorToolsZh = {
  title: '颜色工具',
  description: '颜色选择、格式转换、调色板生成工具',
  color_selection: '颜色选择',
  random_color: '随机颜色',
  hex: 'HEX',
  rgb: 'RGB',
  hsl: 'HSL',
  color_formats: {
    hex: 'HEX',
    rgb: 'RGB',
    hsl: 'HSL'
  },
  color_palette: '颜色调色板',
  palette_actions: {
    add_color: '添加颜色',
    random_color: '随机颜色',
    save_palette: '保存调色板',
    clear_palette: '清空调色板',
    save_to_local: '保存到本地'
  },
  save_palette_success: '调色板已保存到本地',
  save_palette_error: '保存调色板失败',
  palette_color_name: '颜色名称',
  palette_color_name_input: '输入颜色名称...',
  add: '添加',
  cancel: '取消',
  example_palette: {
    primary: '主色',
    secondary: '辅助色',
    accent: '强调色',
    dark: '深色',
    light: '浅色'
  },
  color_preview: '颜色预览',
  contrast_effects: '对比效果',
  white_text: '白色文本',
  black_text: '黑色文本',
  border_background: '边框和背景',
  border_effect: '边框效果',
  transparent_background: '透明度背景',
  color_variants: '颜色变体',
  complementary_color: '互补色',
  analogous_harmony: '邻近和谐色',
  usage_guide: {
    title: '颜色工具使用说明',
    content: '本工具提供颜色选择、格式转换和调色板功能，支持：',
    features: [
      'HEX、RGB、HSL三种颜色格式的转换和复制',
      '亮度色阶展示，快速选择不同亮度的相同颜色',
      '互补色和邻近色展示，帮助设计和谐的配色方案',
      '个性化调色板保存，支持添加和删除自定义颜色'
    ]
  },
  copy_success: '已复制',
  copy_failed: '复制失败'
};

export default colorToolsZh; 