export const colorToolsEn = {
  title: 'Color Tools',
  description: 'Color picker, format converter, palette generator',
  color_selection: 'Color Selection',
  random_color: 'Random Color',
  hex: 'HEX',
  rgb: 'RGB',
  hsl: 'HSL',
  color_formats: {
    hex: 'HEX',
    rgb: 'RGB',
    hsl: 'HSL'
  },
  color_palette: 'Color Palette',
  palette_actions: {
    add_color: 'Add Color',
    random_color: 'Random Color',
    save_palette: 'Save Palette',
    clear_palette: 'Clear Palette',
    save_to_local: 'Save to Local'
  },
  save_palette_success: 'Palette saved to local storage',
  save_palette_error: 'Failed to save palette',
  palette_color_name: 'Color Name',
  palette_color_name_input: 'Enter color name...',
  add: 'Add',
  cancel: 'Cancel',
  example_palette: {
    primary: 'Primary',
    secondary: 'Secondary',
    accent: 'Accent',
    dark: 'Dark',
    light: 'Light'
  },
  color_preview: 'Color Preview',
  contrast_effects: 'Contrast Effects',
  white_text: 'White Text',
  black_text: 'Black Text',
  border_background: 'Border and Background',
  border_effect: 'Border Effect',
  transparent_background: 'Transparent Background',
  color_variants: 'Color Variants',
  complementary_color: 'Complementary Color',
  analogous_harmony: 'Analogous Harmony',
  usage_guide: {
    title: 'Color Tools Usage Guide',
    content: 'This tool provides color selection, format conversion, and palette functions, supporting:',
    features: [
      'Conversion and copying of HEX, RGB, and HSL color formats',
      'Brightness scale display, quickly select different brightness of the same color',
      'Complementary and adjacent harmony colors to help design harmonious color schemes',
      'Personalized palette saving, support for adding and deleting custom colors'
    ]
  },
  copy_success: 'Copied',
  copy_failed: 'Copy failed'
};

export default colorToolsEn; 