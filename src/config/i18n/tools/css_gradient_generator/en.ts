export const cssGradientGeneratorEn = {
  title: 'CSS Gradient Generator',
  description: 'Generate CSS gradient code online, supporting linear and radial gradients',
  gradient_type: 'Gradient Type',
  linear_gradient: 'Linear Gradient',
  radial_gradient: 'Radial Gradient',
  gradient_direction: 'Gradient Direction',
  gradient_shape_position: 'Gradient Shape & Position',
  custom_angle: 'Custom Angle',
  apply: 'Apply',
  gradient_shape: 'Gradient Shape',
  circle: 'Circle',
  ellipse: 'Ellipse',
  gradient_position: 'Gradient Position',
  gradient_colors: 'Gradient Colors',
  add_color_stop: 'Add Color Stop',
  random_gradient: 'Random Gradient',
  preset_colors: 'Preset Color Combinations',
  apply_preset: 'Apply Preset Colors',
  gradient_preview: 'Gradient Preview',
  preview_hint: 'Click the gradient preview above to view full-screen effect. Adjust parameters on the left to preview gradient changes in real-time.',
  css_code: 'CSS Code',
  css_comment: '/* CSS Gradient Code */',
  copy_code: 'Copy Code',
  copied: 'Copied',
  copy_failed: 'Copy Failed',
  direction_titles: {
    '225deg': 'Top Left to Bottom Right',
    '270deg': 'Top to Bottom',
    '315deg': 'Top Right to Bottom Left',
    '180deg': 'Left to Right',
    '0deg': 'Right to Left',
    '135deg': 'Bottom Left to Top Right',
    '90deg': 'Bottom to Top',
    '45deg': 'Bottom Right to Top Left'
  }
};

export default cssGradientGeneratorEn; 