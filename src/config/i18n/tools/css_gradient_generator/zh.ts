export const cssGradientGeneratorZh = {
  title: 'CSS渐变生成器',
  description: '在线生成CSS渐变代码，支持线性和径向渐变',
  gradient_type: '渐变类型',
  linear_gradient: '线性渐变',
  radial_gradient: '径向渐变',
  gradient_direction: '渐变方向',
  gradient_shape_position: '渐变形状与位置',
  custom_angle: '自定义角度',
  apply: '应用',
  gradient_shape: '渐变形状',
  circle: '圆形',
  ellipse: '椭圆形',
  gradient_position: '渐变位置',
  gradient_colors: '渐变颜色',
  add_color_stop: '添加颜色停止点',
  random_gradient: '随机渐变',
  preset_colors: '预设颜色组合',
  apply_preset: '应用预设颜色',
  gradient_preview: '渐变预览',
  preview_hint: '点击上方的渐变预览可查看全屏效果。调整左侧的参数可实时预览渐变效果变化。',
  css_code: 'CSS代码',
  css_comment: '/* CSS渐变代码 */',
  copy_code: '复制代码',
  copied: '已复制',
  copy_failed: '复制失败',
  direction_titles: {
    '225deg': '左上到右下',
    '270deg': '上到下',
    '315deg': '右上到左下',
    '180deg': '左到右',
    '0deg': '右到左',
    '135deg': '左下到右上',
    '90deg': '下到上',
    '45deg': '右下到左上'
  }
};

export default cssGradientGeneratorZh; 