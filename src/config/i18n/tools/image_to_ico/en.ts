export const imageToIcoEn = {
  title: 'Image to ICO icon',
  description: 'Convert images to ICO icon format for website favicons and more',
  select_image: 'Select Image File',
  choose_file: 'Choose File',
  conversion_settings: 'Conversion Settings',
  ico_size: 'Icon Size',
  maintain_ratio: 'Maintain Aspect Ratio',
  image_info: 'Image Information',
  original_size: 'Original Size',
  original_dimensions: 'Original Dimensions',
  preview: 'Preview',
  convert_to_ico: 'Convert to ICO',
  converting: 'Converting...',
  download_ico: 'Download ICO Icon',
  reset: 'Reset',
  original_image: 'Original Image',
  ico_preview: 'ICO Preview',
  no_image_selected: 'No Image Selected',
  no_ico_generated: 'No ICO Generated',
  conversion_failed: 'Conversion Failed',
  bytes: 'Bytes',
  kb: 'KB',
  mb: 'MB',
  gb: 'GB',
  usage_guide: 'Usage Guide',
  guide_1: 'Upload a JPG, PNG, or other image format',
  guide_2: 'Choose the ICO icon size (typically 16x16, 32x32, 48x48, or 64x64 pixels)',
  guide_3: 'Click the "Convert to ICO" button to generate the ICO icon',
  guide_4: 'Conversion is performed in the browser, no images are uploaded to servers',
  guide_5: 'Preview and download the ICO icon after generation',
  guide_6: 'ICO icons are commonly used for website favicons, application icons, etc.',
  pixelated_notice: 'Note: Small icons may appear pixelated, best results come from simple, clear images'
};

export default imageToIcoEn; 