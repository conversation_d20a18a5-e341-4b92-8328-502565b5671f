export const imageToIcoZh = {
  title: '图片转ICO图标',
  description: '将图片转换为ICO图标格式，适用于网站favicon等场景',
  select_image: '选择图片文件',
  choose_file: '选择文件',
  conversion_settings: '转换设置',
  ico_size: '图标尺寸',
  maintain_ratio: '保持宽高比',
  image_info: '图片信息',
  original_size: '原始大小',
  original_dimensions: '原始尺寸',
  preview: '预览',
  convert_to_ico: '转换为ICO',
  converting: '转换中...',
  download_ico: '下载ICO图标',
  reset: '重置',
  original_image: '原始图片',
  ico_preview: 'ICO预览',
  no_image_selected: '未选择图片',
  no_ico_generated: '未生成ICO图标',
  conversion_failed: '转换失败',
  bytes: '字节',
  kb: 'KB',
  mb: 'MB',
  gb: 'GB',
  usage_guide: '使用说明',
  guide_1: '上传JPG、PNG或其他格式的图片',
  guide_2: '选择ICO图标的尺寸（通常为16x16、32x32、48x48或64x64像素）',
  guide_3: '点击"转换为ICO"按钮生成ICO图标',
  guide_4: '转换在浏览器中完成，不会上传图片到服务器',
  guide_5: '生成后可以预览和下载ICO图标',
  guide_6: 'ICO图标通常用作网站favicon、应用程序图标等',
  pixelated_notice: '注意：小尺寸图标可能会出现像素化，建议使用简单清晰的图像'
};

export default imageToIcoZh; 