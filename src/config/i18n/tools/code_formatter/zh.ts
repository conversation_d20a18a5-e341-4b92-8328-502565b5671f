export const codeFormatterZh = {
  title: '代码格式化',
  description: 'HTML/CSS/JS/SQL等多种语言的代码美化',
  warning_missing_files: '警告：格式化工具文件可能缺失，功能可能受限',
  load_failed: '加载格式化库失败，请刷新页面重试',
  error_empty_input: '请输入需要格式化的代码',
  error_browser_only: '格式化功能仅在浏览器中可用',
  error_library_loading: '格式化库尚未加载完成，请稍后再试',
  error_unsupported_language: '不支持的语言类型: {language}',
  error_prettier: '格式化库错误: {message}',
  error_formatting: '格式化错误: {message}',
  error_unknown: '未知错误',
  error_initialization: '初始化失败',
  input_code: '输入代码',
  formatted_result: '格式化结果',
  input_placeholder: '请输入{language}代码...',
  result_placeholder: '格式化后的代码将显示在这里',
  click_format: '点击下方的"格式化"按钮开始处理',
  load_example: '加载示例',
  clear: '清空',
  copy_failed: '复制失败:',
  formatting_error_title: '格式化错误',
  format: '格式化',
  processing: '处理中...',
  loading_library: '加载格式化库...',
  first_time_loading: '首次使用时需要加载格式化库，请稍等...',
  usage_guide: '使用说明',
  usage_step1: '选择代码语言类型，输入或粘贴需要格式化的代码',
  usage_step2: '点击"格式化"按钮处理代码',
  usage_step3: '格式化后可以复制或下载结果',
  usage_step4: '如果遇到错误，请检查代码语法',
  prettier_core_loaded: 'Prettier核心加载成功',
  babel_parser_loaded: 'Babel解析器加载成功',
  html_parser_loaded: 'HTML解析器加载成功',
  css_parser_loaded: 'CSS解析器加载成功',
  typescript_parser_loaded: 'TypeScript解析器加载成功',
  markdown_parser_loaded: 'Markdown解析器加载成功',
  yaml_parser_loaded: 'YAML解析器加载成功',
  graphql_parser_loaded: 'GraphQL解析器加载成功',
  all_modules_loaded: '所有Prettier模块加载完成!',
  load_error: '加载Prettier失败:',
  using_parser: '使用parser:',
  available_plugins: '可用的插件:',
  prettier_error_log: 'Prettier错误:',
  copy: '复制',
  copied: '已复制',
  download: '下载',
  language_select: '选择语言'
};

export default codeFormatterZh; 