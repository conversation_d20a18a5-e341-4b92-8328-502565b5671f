export const codeFormatterEn = {
  title: 'Code Formatter',
  description: 'Code beautifier for HTML/CSS/JS/SQL and more',
  warning_missing_files: 'Warning: Formatting tool files may be missing, functionality may be limited',
  load_failed: 'Failed to load formatting library, please refresh the page and try again',
  error_empty_input: 'Please enter code to format',
  error_browser_only: 'Formatting is only available in browsers',
  error_library_loading: 'Formatting library has not finished loading, please try again later',
  error_unsupported_language: 'Unsupported language type: {language}',
  error_prettier: 'Formatting library error: {message}',
  error_formatting: 'Formatting error: {message}',
  error_unknown: 'Unknown error',
  error_initialization: 'Initialization failed',
  input_code: 'Input Code',
  formatted_result: 'Formatted Result',
  input_placeholder: 'Please enter {language} code...',
  result_placeholder: 'Formatted code will be displayed here',
  click_format: 'Click the "Format" button below to process',
  load_example: 'Load Example',
  clear: 'Clear',
  copy_failed: 'Copy failed:',
  formatting_error_title: 'Formatting Error',
  format: 'Format',
  processing: 'Processing...',
  loading_library: 'Loading formatting library...',
  first_time_loading: 'First time use requires loading the formatting library, please wait...',
  usage_guide: 'Usage Guide',
  usage_step1: 'Select the code language type, enter or paste the code you want to format',
  usage_step2: 'Click the "Format" button to process the code',
  usage_step3: 'After formatting, you can copy or download the result',
  usage_step4: 'If you encounter errors, check your code syntax',
  prettier_core_loaded: 'Prettier core loaded successfully',
  babel_parser_loaded: 'Babel parser loaded successfully',
  html_parser_loaded: 'HTML parser loaded successfully',
  css_parser_loaded: 'CSS parser loaded successfully',
  typescript_parser_loaded: 'TypeScript parser loaded successfully',
  markdown_parser_loaded: 'Markdown parser loaded successfully',
  yaml_parser_loaded: 'YAML parser loaded successfully',
  graphql_parser_loaded: 'GraphQL parser loaded successfully',
  all_modules_loaded: 'All Prettier modules loaded!',
  load_error: 'Failed to load Prettier:',
  using_parser: 'Using parser:',
  available_plugins: 'Available plugins:',
  prettier_error_log: 'Prettier error:',
  copy: 'Copy',
  copied: 'Copied',
  download: 'Download',
  language_select: 'Select Language'
};

export default codeFormatterEn; 