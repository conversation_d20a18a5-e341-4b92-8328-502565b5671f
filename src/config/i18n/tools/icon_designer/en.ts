export const iconDesignerEn = {
  title: 'Icon Designer',
  description: 'Quickly design clean and beautiful app icons with various background shapes and color combinations',
  icon_selection: 'Icon Selection',
  choose_icon: 'Choose Icon',
  icon_search_placeholder: 'Search icons...',
  popular_icons: 'Popular Icons',
  business_icons: 'Business Icons',
  tech_icons: 'Tech Icons',
  social_icons: 'Social Icons',
  ui_icons: 'UI Icons',
  media_icons: 'Media Icons',
  transport_icons: 'Transport Icons',
  shopping_icons: 'Shopping Icons',
  health_icons: 'Health Icons',
  food_icons: 'Food Icons',
  sports_icons: 'Sports Icons',
  weather_icons: 'Weather Icons',
  time_icons: 'Time Icons',
  security_icons: 'Security Icons',
  files_icons: 'File Icons',
  
  background_settings: 'Background Settings',
  background_shape: 'Background Shape',
  background_color: 'Background Color',
  background_type: 'Background Type',
  background_solid: 'Solid',
  background_gradient: 'Gradient',
  gradient_direction: 'Gradient Direction',
  gradient_start: 'Start Color',
  gradient_end: 'End Color',
  gradient_linear: 'Linear Gradient',
  gradient_radial: 'Radial Gradient',
  
  shape_circle: 'Circle',
  shape_square: 'Square',
  shape_rounded_square: 'Rounded Square',
  shape_hexagon: 'Hexagon',
  
  icon_settings: 'Icon Settings',
  icon_type: 'Icon Type',
  icon_type_fontawesome: 'FontAwesome Icon',
  icon_type_text: 'Custom Text',
  icon_color: 'Icon Color',
  icon_size: 'Icon Size',
  icon_position: 'Icon Position',
  icon_rotation: 'Icon Rotation',
  text_input: 'Enter Text',
  text_input_placeholder: 'Enter your text...',
  font_family: 'Font Family',
  font_weight: 'Font Weight',
  font_size: 'Font Size',
  
  color_black: 'Black',
  color_white: 'White',
  color_gray: 'Gray',
  color_blue: 'Blue',
  color_green: 'Green',
  color_red: 'Red',
  color_orange: 'Orange',
  color_purple: 'Purple',
  color_custom: 'Custom',
  
  preset_templates: 'Preset Templates',
  template_ios_style: 'iOS Style',
  template_material: 'Material Style',
  template_minimal: 'Minimal Style',
  template_gradient: 'Gradient Style',
  template_neon: 'Neon Style',
  template_retro: 'Retro Style',
  template_glassmorphism: 'Glassmorphism',
  template_neumorphism: 'Neumorphism',
  
  export_settings: 'Export Settings',
  export_size: 'Export Size',
  export_format: 'Export Format',
  download_icon: 'Download Icon',
  
  size_small: 'Small (64x64)',
  size_medium: 'Medium (128x128)',
  size_large: 'Large (256x256)',
  size_xlarge: 'X-Large (512x512)',
  
  format_png: 'PNG Format',
  format_svg: 'SVG Format',
  format_ico: 'ICO Format',
  
  preview: 'Preview',
  no_icon_selected: 'No Icon Selected',
  select_icon_first: 'Please select an icon first',
  
  usage_guide: 'Usage Guide',
  guide_1: '1. Choose icon type: FontAwesome icon or custom text',
  guide_2: '2. Configure icon content, color, size and rotation',
  guide_3: '3. Select background shape and type (solid or gradient)',
  guide_4: '4. Adjust background color or gradient settings',
  guide_5: '5. Apply preset templates for quick design',
  guide_6: '6. Select export size and format, then download',
  
  tips: 'Tips',
  tip_1: 'Recommend using high contrast color combinations, like black background with white icon',
  tip_2: 'For mobile apps, rounded square background is recommended',
  tip_3: 'Gradient backgrounds can add depth and modern feel to icons',
  tip_4: 'Custom text feature supports creating brand initials or short word icons',
  tip_5: 'Appropriate rotation angles can add dynamic feel to icons',
  tip_6: 'Neumorphism and glassmorphism templates suit modern UI design',
  
  applying_template: 'Applying template...',
  generating_icon: 'Generating icon...',
  download_ready: 'Download ready'
}; 