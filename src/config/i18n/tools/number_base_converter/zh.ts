export const numberBaseConverterZh = {
  title: '进制转换工具',
  description: '二进制、八进制、十进制、十六进制等数值进制转换工具',
  from_base: '从进制',
  to_base: '转换为',
  convert: '转换',
  copy: '复制结果',
  clear: '清空',
  load_example: '加载示例',
  input_placeholder: '输入要转换的数值...',
  output_placeholder: '转换结果将在这里显示...',
  binary: '二进制 (2)',
  octal: '八进制 (8)',
  decimal: '十进制 (10)',
  hex: '十六进制 (16)',
  custom: '自定义',
  custom_base_from: '自定义进制 (2-36)',
  custom_base_to: '目标进制 (2-36)',
  base_error: '进制必须是2到36之间的整数',
  input_error: '输入无效，请确保数值符合所选进制',
  general_error: '转换过程中发生错误',
  example_decimal: '255',
  example_binary: '11111111',
  example_octal: '377',
  example_hex: 'FF',
  result_label: '转换结果',
  input_label: '数值输入',
  copy_success: '已复制到剪贴板',
  copy_failed: '复制失败:',
  clipboard_error: '复制到剪贴板失败',
  advanced_options: '高级选项',
  use_uppercase: '使用大写字母（用于十六进制以上进制）',
  add_prefix: '添加前缀（0b, 0o, 0x等）',
  group_digits: '分组数字（每4位或8位）'
};

export default numberBaseConverterZh; 