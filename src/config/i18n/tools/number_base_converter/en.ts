export const numberBaseConverterEn = {
  title: 'Number Base Converter',
  description: 'Convert between binary, octal, decimal, hexadecimal and other number bases',
  from_base: 'From Base',
  to_base: 'To Base',
  convert: 'Convert',
  copy: 'Copy Result',
  clear: 'Clear',
  load_example: 'Load Example',
  input_placeholder: 'Enter a value to convert...',
  output_placeholder: 'Conversion result will appear here...',
  binary: 'Binary (2)',
  octal: 'Octal (8)',
  decimal: 'Decimal (10)',
  hex: 'Hexadecimal (16)',
  custom: 'Custom',
  custom_base_from: 'Custom Base (2-36)',
  custom_base_to: 'Target Base (2-36)',
  base_error: 'Base must be an integer between 2 and 36',
  input_error: 'Invalid input, please ensure the value matches the selected base',
  general_error: 'An error occurred during conversion',
  example_decimal: '255',
  example_binary: '11111111',
  example_octal: '377',
  example_hex: 'FF',
  result_label: 'Conversion Result',
  input_label: 'Value Input',
  copy_success: 'Copied to clipboard',
  copy_failed: 'Copy failed:',
  clipboard_error: 'Failed to copy to clipboard',
  advanced_options: 'Advanced Options',
  use_uppercase: 'Use uppercase letters (for hex and higher bases)',
  add_prefix: 'Add prefixes (0b, 0o, 0x etc.)',
  group_digits: 'Group digits (every 4 or 8 digits)'
};

export default numberBaseConverterEn; 