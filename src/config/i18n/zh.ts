import commonZh from './common/zh';
import tools from './tools';

export const zh = {
  ...commonZh,
  tools: {
    json_formatter: tools.json_formatter.zh,
    regex_tester: tools.regex_tester.zh,
    timestamp_converter: tools.timestamp_converter.zh,
    encoding_converter: tools.encoding_converter.zh,
    code_formatter: tools.code_formatter.zh,
    color_tools: tools.color_tools.zh,
    crypto_tools: tools.crypto_tools.zh,
    css_gradient_generator: tools.css_gradient_generator.zh,
    date_calculator: tools.date_calculator.zh,
    html_markdown_converter: tools.html_markdown_converter.zh,
    http_tester: tools.http_tester.zh,
    image_compressor: tools.image_compressor.zh,
    ip_lookup: tools.ip_lookup.zh,
    json_converter: tools.json_converter.zh,
    json_editor: tools.json_editor.zh,
    jwt_decoder: tools.jwt_decoder.zh,
    qrcode_generator: tools.qrcode_generator.zh,
    text_counter: tools.text_counter.zh,
    text_space_stripper: tools.text_space_stripper.zh,
    timezone_converter: tools.timezone_converter.zh,
    unicode_converter: tools.unicode_converter.zh,
    url_encoder: tools.url_encoder.zh,
    number_base_converter: tools.number_base_converter.zh,
    yml_properties_converter: tools.yml_properties_converter.zh,
    base64_to_image: tools.base64_to_image.zh,
    image_watermark: tools.image_watermark.zh,
    image_to_ico: tools.image_to_ico.zh,
    cron_generator: tools.cron_generator.zh,
    icon_designer: tools.icon_designer.zh,

    pdf_converter: tools.pdf_converter.zh,
    pdf_manager: tools.pdf_manager.zh,
    pdf_compressor: tools.pdf_compressor.zh,

  }
};

export default zh; 