'use client';

import React, { useState, useEffect } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faStar, faSearch, faTimes, faChevronDown, faBook, faCode, faCloud, faBell, faExternalLinkAlt, faCube } from '@fortawesome/free-solid-svg-icons';
import { faStar as farStar } from '@fortawesome/free-regular-svg-icons';
import { faGithub } from '@fortawesome/free-brands-svg-icons';
import categories from '@/config/categories';
import tools from '@/config/tools';
import { useRouter } from 'next/navigation';
import ThemeToggle from '@/components/ThemeToggle';
import LanguageToggle from '@/components/LanguageToggle';
import BackToTop from '@/components/BackToTop';
import Link from 'next/link';
import { useLanguage } from '@/context/LanguageContext';

export default function Home() {
  const { t, language } = useLanguage();
  const [activeCategory, setActiveCategory] = useState(() => {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('lastActiveCategory') || "all";
    }
    return "all";
  });
  const [searchTerm, setSearchTerm] = useState("");
  const [iconsLoaded, setIconsLoaded] = useState(false);
  const [favoriteTools, setFavoriteTools] = useState<string[]>([]);
  const [showFavorites, setShowFavorites] = useState(false);
  const [showNotification, setShowNotification] = useState(false);
  const [firstFavoriteAdded, setFirstFavoriteAdded] = useState(false);
  const [prefetchedTools, setPrefetchedTools] = useState<Set<string>>(new Set());
  const [loadingProgress, setLoadingProgress] = useState<{ current: number, total: number }>({ current: 0, total: 0 });
  const [showProductsDropdown, setShowProductsDropdown] = useState(false);
  const router = useRouter();

  // 产品推荐列表
  const recommendedProducts = [];

  // 点击其他区域关闭产品推荐下拉菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (!target.closest('#products-dropdown-container')) {
        setShowProductsDropdown(false);
      }
    };

    document.addEventListener('click', handleClickOutside);
    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  }, []);

  // 切换产品推荐下拉菜单的显示状态
  const toggleProductsDropdown = (e: React.MouseEvent) => {
    e.stopPropagation();
    setShowProductsDropdown(prev => !prev);
  };

  // 图标加载处理
  useEffect(() => {
    const timer = setTimeout(() => {
      setIconsLoaded(true);
    }, 200);
    
    return () => clearTimeout(timer);
  }, []);

  // 加载收藏工具和首次收藏状态
  useEffect(() => {
    const savedFavorites = localStorage.getItem('favoriteTools');
    const hasSeenFirstFavoriteNotification = localStorage.getItem('hasSeenFirstFavoriteNotification') === 'true';
    
    setFirstFavoriteAdded(hasSeenFirstFavoriteNotification);
    
    if (savedFavorites) {
      setFavoriteTools(JSON.parse(savedFavorites));
      setShowFavorites(true);
    }
  }, []);

  // 收藏/取消收藏工具
  const toggleFavorite = (toolCode: string, e: React.MouseEvent) => {
    e.stopPropagation();
    
    const isAdding = !favoriteTools.includes(toolCode);
    
    const updatedFavorites = isAdding
      ? [...favoriteTools, toolCode]
      : favoriteTools.filter(code => code !== toolCode);
    
    setFavoriteTools(updatedFavorites);
    localStorage.setItem('favoriteTools', JSON.stringify(updatedFavorites));
    
    setShowFavorites(updatedFavorites.length > 0);
    
    if (isAdding && !firstFavoriteAdded) {
      setShowNotification(true);
      setFirstFavoriteAdded(true);
      localStorage.setItem('hasSeenFirstFavoriteNotification', 'true');
      
      setTimeout(() => {
        setShowNotification(false);
      }, 5000);
    }
  };

  // 关闭通知
  const closeNotification = () => {
    setShowNotification(false);
  };

  // 切换到收藏分类
  const viewFavorites = () => {
    setActiveCategory('favorites');
  };

  // 预加载工具页面的函数
  const prefetchTool = (toolCode: string) => {
    if (prefetchedTools.has(toolCode)) {
      return;
    }
    
    const prefetchUrl = `/tools/${toolCode}`;
    fetch(prefetchUrl, { priority: 'low' })
      .then(response => {
        if (!response.ok) {
          throw new Error(`预加载失败: ${response.status}`);
        }
        return response.text();
      })
      .then(() => {
        setPrefetchedTools(prev => {
          const newSet = new Set(prev);
          newSet.add(toolCode);
          return newSet;
        });
      })
      .catch(error => {
        console.warn(`工具 ${toolCode} 预加载失败:`, error);
      });
  };

  // 处理工具导航
  const navigateToTool = (toolCode: string) => {
    localStorage.setItem('from_homepage', 'true');
    router.push(`/tools/${toolCode}`);
  };

  // 过滤工具列表
  const filteredTools = () => tools.filter(tool => {
    if (searchTerm && !t(`tools.${tool.code}.title`).toLowerCase().includes(searchTerm.toLowerCase()) && 
        !t(`tools.${tool.code}.description`).toLowerCase().includes(searchTerm.toLowerCase()) &&
        !tool.keywords?.some(keyword => keyword.toLowerCase().includes(searchTerm.toLowerCase()))) {
      return false;
    }
    
    if (activeCategory === 'favorites') {
      return favoriteTools.includes(tool.code);
    }
    
    if (activeCategory !== "all" && !tool.category.includes(activeCategory)) {
      return false;
    }
    
    return true;
  });

  // 构建分类列表
  const allCategories = [
    ...categories.slice(0, 2),
    ...(showFavorites ? [{ code: "favorites", name: t('common.favorites'), active: false }] : []),
    ...categories.slice(2)
  ];

  // 更新分类选择处理函数
  const handleCategoryChange = (categoryCode: string) => {
    setActiveCategory(categoryCode);
    localStorage.setItem('lastActiveCategory', categoryCode);
  };

  return (
    <div className={`min-h-screen relative ${!iconsLoaded ? 'opacity-90' : 'opacity-100 transition-opacity duration-500'}`}>
      {/* 全新动态背景 - 科技感网格 */}
      <div className="fixed inset-0 z-0">
        {/* 主背景渐变 */}
        <div className="absolute inset-0 bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900"></div>
        
        {/* 动态网格背景 */}
        <div className="absolute inset-0 opacity-20">
          <div className="absolute inset-0" style={{
            backgroundImage: `
              linear-gradient(rgba(139, 92, 246, 0.3) 1px, transparent 1px),
              linear-gradient(90deg, rgba(139, 92, 246, 0.3) 1px, transparent 1px)
            `,
            backgroundSize: '50px 50px',
            animation: 'grid-move 20s linear infinite'
          }}></div>
        </div>
        
        {/* 浮动粒子效果 */}
        <div className="absolute inset-0">
          {[...Array(6)].map((_, i) => (
            <div
              key={i}
              className="absolute w-2 h-2 bg-purple-400 rounded-full opacity-60"
              style={{
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
                animation: `float-particle ${3 + Math.random() * 4}s ease-in-out infinite`,
                animationDelay: `${Math.random() * 2}s`
              }}
            ></div>
          ))}
        </div>
        
        {/* 光束效果 */}
        <div className="absolute top-0 left-1/4 w-px h-full bg-gradient-to-b from-transparent via-purple-500 to-transparent opacity-30 animate-pulse"></div>
        <div className="absolute top-0 right-1/3 w-px h-full bg-gradient-to-b from-transparent via-blue-500 to-transparent opacity-30 animate-pulse delay-1000"></div>
        
        {/* 发光圆环 */}
        <div className="absolute top-20 right-20 w-64 h-64 border border-purple-500/30 rounded-full animate-spin-slow"></div>
        <div className="absolute bottom-20 left-20 w-48 h-48 border border-blue-500/30 rounded-full animate-spin-slow-reverse"></div>
      </div>

      {/* 主容器 */}
      <div className="relative z-10 min-h-screen flex flex-col max-w-[1440px] mx-auto px-4 sm:px-8 py-4 sm:py-6">
        {/* 通知提示 */}
        {showNotification && (
          <div className="fixed top-4 left-1/2 transform -translate-x-1/2 z-50 flex items-center gap-2 bg-gradient-to-r from-purple-500 to-blue-500 text-white px-6 py-4 rounded-2xl shadow-2xl backdrop-blur-sm animate-fadeIn border border-white/20">
            <FontAwesomeIcon icon={faBell} className="text-lg" />
            <span className="font-medium">工具已添加到收藏夹，点击导航栏中的&quot;我的收藏&quot;查看</span>
            <button 
              className="ml-2 text-white hover:text-gray-200 transition-colors p-1 rounded-full hover:bg-white/10"
              onClick={closeNotification}
            >
              <FontAwesomeIcon icon={faTimes} />
            </button>
          </div>
        )}

        {/* 全新头部设计 */}
        <header className="relative z-20">
          {/* 顶部装饰线 */}
          <div className="h-1 bg-gradient-to-r from-purple-500 via-blue-500 to-purple-500 animate-gradient-x"></div>
          
          <div className="backdrop-blur-2xl bg-black/20 border-b border-white/10">
            <div className="container mx-auto px-6 py-6">
              {/* Logo和标题区域 */}
              <div className="flex items-center justify-between mb-6">
                <div className="flex items-center space-x-4">
                  {/* 全新Logo设计 */}
                  <div className="relative">
                    <div className="w-14 h-14 bg-gradient-to-br from-purple-500 to-blue-600 rounded-2xl flex items-center justify-center shadow-2xl transform rotate-3 hover:rotate-0 transition-transform duration-300">
                      <FontAwesomeIcon icon={faCube} className="text-white text-xl" />
                      <div className="absolute -inset-1 bg-gradient-to-br from-purple-500 to-blue-600 rounded-2xl blur opacity-30 animate-pulse"></div>
                    </div>
                  </div>
                  
                  <div>
                    <h1 className="text-3xl font-black bg-gradient-to-r from-white via-purple-200 to-blue-200 bg-clip-text text-transparent">
                      {t('common.siteName')}
                    </h1>
                    <p className="text-sm text-gray-400 font-medium tracking-wide">{t('common.siteDesc')}</p>
                  </div>
                </div>
                
                {/* 右侧控制按钮 */}
                <div className="flex items-center space-x-3">
                  <LanguageToggle />
                  <ThemeToggle />
                  
                  {/* 收藏按钮 */}
                  <button 
                    className="group relative w-12 h-12 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-xl flex items-center justify-center shadow-lg hover:shadow-2xl transition-all duration-300 hover:scale-110"
                    onClick={viewFavorites}
                    title={t('common.favorites')}
                  >
                    <FontAwesomeIcon icon={faStar} className="text-white text-lg group-hover:scale-110 transition-transform" />
                    <div className="absolute -inset-1 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-xl blur opacity-0 group-hover:opacity-50 transition-opacity"></div>
                  </button>
                </div>
              </div>
              
              {/* 全新搜索框设计 */}
              <div className="relative max-w-2xl mx-auto">
                <div className="relative group">
                  <input
                    type="text"
                    placeholder={t('common.search')}
                    className="w-full h-14 bg-black/30 backdrop-blur-xl rounded-full pl-16 pr-16 text-white text-lg outline-none border-2 border-white/20 focus:border-purple-400 focus:ring-4 focus:ring-purple-400/20 transition-all duration-300 shadow-2xl placeholder:text-gray-400"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                  
                  {/* 搜索图标 */}
                  <div className="absolute left-5 top-1/2 transform -translate-y-1/2 text-gray-400 group-focus-within:text-purple-400 transition-colors">
                    <FontAwesomeIcon icon={faSearch} className="text-xl" />
                  </div>
                  
                  {/* 清除按钮 */}
                  {searchTerm && (
                    <button 
                      className="absolute right-5 top-1/2 transform -translate-y-1/2 w-8 h-8 rounded-full bg-white/20 hover:bg-white/30 flex items-center justify-center transition-all duration-200 hover:scale-110"
                      onClick={() => setSearchTerm('')}
                    >
                      <FontAwesomeIcon icon={faTimes} className="text-white text-sm" />
                    </button>
                  )}
                  
                  {/* 搜索框发光效果 */}
                  <div className="absolute -inset-1 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full blur opacity-0 group-focus-within:opacity-30 transition-opacity duration-300"></div>
                </div>
              </div>
            </div>
          </div>
        </header>

        {/* 主内容区域 */}
        <main className="relative z-10 flex-1 container mx-auto px-6 py-8">
          {/* 全新分类导航设计 */}
          <div className="mb-12">
            <div className="flex flex-wrap justify-center gap-4">
              {allCategories.map((category, index) => (
                <button 
                  key={index}
                  className={`group relative px-8 py-4 rounded-2xl font-bold text-lg transition-all duration-300 transform hover:scale-105 ${
                    activeCategory === category.code 
                      ? 'bg-gradient-to-r from-purple-500 to-blue-500 text-white shadow-2xl scale-105' 
                      : 'bg-black/30 backdrop-blur-xl text-white border border-white/20 hover:border-purple-400/50 hover:bg-black/50'
                  }`}
                  onClick={() => handleCategoryChange(category.code)}
                >
                  {/* 按钮内容 */}
                  <span className="relative z-10">{t(`categories.${category.code}`)}</span>
                  
                  {/* 激活状态的发光效果 */}
                  {activeCategory === category.code && (
                    <div className="absolute -inset-1 bg-gradient-to-r from-purple-500 to-blue-500 rounded-2xl blur opacity-50 animate-pulse"></div>
                  )}
                  
                  {/* 悬停效果 */}
                  {activeCategory !== category.code && (
                    <div className="absolute inset-0 bg-gradient-to-r from-purple-500/20 to-blue-500/20 rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                  )}
                  
                  {/* 底部指示器 */}
                  {activeCategory === category.code && (
                    <div className="absolute -bottom-2 left-1/2 transform -translate-x-1/2 w-2 h-2 bg-white rounded-full animate-bounce"></div>
                  )}
                </button>
              ))}
            </div>
          </div>

          {/* 全新工具卡片设计 */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-8">
            {filteredTools().map((tool, index) => (
              <div
                key={index}
                className="group relative cursor-pointer transform transition-all duration-500 hover:scale-105"
                onClick={() => navigateToTool(tool.code)}
                onMouseEnter={() => prefetchTool(tool.code)}
              >
                {/* 卡片主体 */}
                <div className="relative h-64 bg-gradient-to-br from-black/40 to-black/60 backdrop-blur-2xl rounded-3xl p-6 border border-white/10 hover:border-purple-400/50 transition-all duration-300 overflow-hidden">

                  {/* 背景装饰 */}
                  <div className="absolute top-0 right-0 w-32 h-32 bg-gradient-to-br from-purple-500/20 to-blue-500/20 rounded-full blur-2xl transform translate-x-8 -translate-y-8 group-hover:scale-150 transition-transform duration-500"></div>

                  {/* 网格装饰 */}
                  <div className="absolute inset-0 opacity-10 group-hover:opacity-20 transition-opacity duration-300"
                    style={{
                      backgroundImage: `
                        linear-gradient(rgba(255, 255, 255, 0.1) 1px, transparent 1px),
                        linear-gradient(90deg, rgba(255, 255, 255, 0.1) 1px, transparent 1px)
                      `,
                      backgroundSize: '20px 20px'
                    }}>
                  </div>

                  {/* 添加不可见的 Link 组件用于 Next.js 原生预加载 */}
                  <Link
                    href={`/tools/${tool.code}`}
                    prefetch={true}
                    className="hidden"
                    aria-hidden="true"
                  />

                  <div className="relative z-10 flex flex-col h-full">
                    {/* 工具图标 */}
                    <div className="flex items-start justify-between mb-6">
                      <div className="relative">
                        <div className="w-16 h-16 bg-gradient-to-br from-purple-500 to-blue-600 rounded-2xl flex items-center justify-center shadow-2xl group-hover:shadow-purple-500/50 transition-all duration-300 group-hover:scale-110 group-hover:rotate-3">
                          <FontAwesomeIcon icon={tool.icon} className="text-white text-2xl" />
                        </div>
                        {/* 图标发光效果 */}
                        <div className="absolute -inset-2 bg-gradient-to-br from-purple-500 to-blue-600 rounded-2xl blur opacity-0 group-hover:opacity-30 transition-opacity duration-300"></div>
                      </div>

                      {/* 收藏按钮 */}
                      <button
                        className="w-10 h-10 rounded-xl bg-black/30 backdrop-blur-sm hover:bg-black/50 flex items-center justify-center transition-all duration-300 hover:scale-110 border border-white/10 hover:border-yellow-400/50"
                        onClick={(e) => toggleFavorite(tool.code, e)}
                      >
                        <FontAwesomeIcon
                          icon={favoriteTools.includes(tool.code) ? faStar : farStar}
                          className={`transition-colors duration-300 ${
                            favoriteTools.includes(tool.code)
                              ? 'text-yellow-400'
                              : 'text-gray-400 hover:text-yellow-400'
                          }`}
                        />
                      </button>
                    </div>

                    {/* 工具标题 */}
                    <h3 className="text-xl font-bold text-white mb-3 group-hover:text-purple-200 transition-colors duration-300">
                      {t(`tools.${tool.code}.title`)}
                    </h3>

                    {/* 分类标签 */}
                    <div className="flex flex-wrap gap-2 mb-4">
                      {tool.category.slice(0, 2).map((catCode, catIndex) => (
                        <span
                          key={catIndex}
                          className="px-3 py-1 text-xs font-medium rounded-full bg-white/10 text-purple-200 border border-white/20"
                        >
                          {t(`categories.${catCode}`)}
                        </span>
                      ))}
                    </div>

                    {/* 工具描述 */}
                    <p className="text-sm text-gray-300 leading-relaxed mt-auto group-hover:text-white transition-colors duration-300">
                      {t(`tools.${tool.code}.description`)}
                    </p>

                    {/* 悬停时的底部光效 */}
                    <div className="absolute bottom-0 left-0 right-0 h-1 bg-gradient-to-r from-purple-500 to-blue-500 transform scale-x-0 group-hover:scale-x-100 transition-transform duration-500 origin-left rounded-full"></div>
                  </div>
                </div>

                {/* 外部发光效果 */}
                <div className="absolute -inset-1 bg-gradient-to-br from-purple-500/20 to-blue-500/20 rounded-3xl blur opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
              </div>
            ))}
          </div>
        </main>

        {/* 返回顶部按钮 */}
        <BackToTop
          scrollThreshold={400}
          position="bottom-right"
          offset={24}
          size="medium"
        />
      </div>
    </div>
  );
}
