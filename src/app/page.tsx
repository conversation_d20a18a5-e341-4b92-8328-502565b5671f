'use client';

import React, { useState, useEffect } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faStar, faSearch, faTimes, faChevronDown, faBook, faCode, faCloud, faBell, faExternalLinkAlt, faCube } from '@fortawesome/free-solid-svg-icons';
import { faStar as farStar } from '@fortawesome/free-regular-svg-icons';
import { faGithub } from '@fortawesome/free-brands-svg-icons';
import categories from '@/config/categories';
import tools from '@/config/tools';
import { useRouter } from 'next/navigation';
import ThemeToggle from '@/components/ThemeToggle';
import LanguageToggle from '@/components/LanguageToggle';
import BackToTop from '@/components/BackToTop';
import Link from 'next/link';
import { useLanguage } from '@/context/LanguageContext';

export default function Home() {
  const { t, language } = useLanguage();
  const [activeCategory, setActiveCategory] = useState(() => {
    if (typeof window !== 'undefined') {
      return localStorage.getItem('lastActiveCategory') || "all";
    }
    return "all";
  });
  const [searchTerm, setSearchTerm] = useState("");
  const [iconsLoaded, setIconsLoaded] = useState(false);
  const [favoriteTools, setFavoriteTools] = useState<string[]>([]);
  const [showFavorites, setShowFavorites] = useState(false);
  const [showNotification, setShowNotification] = useState(false);
  const [firstFavoriteAdded, setFirstFavoriteAdded] = useState(false);
  const [prefetchedTools, setPrefetchedTools] = useState<Set<string>>(new Set());
  const [loadingProgress, setLoadingProgress] = useState<{ current: number, total: number }>({ current: 0, total: 0 });
  const [showProductsDropdown, setShowProductsDropdown] = useState(false);
  const router = useRouter();

  // 产品推荐列表
  const recommendedProducts = [];

  // 点击其他区域关闭产品推荐下拉菜单
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element;
      if (!target.closest('#products-dropdown-container')) {
        setShowProductsDropdown(false);
      }
    };

    document.addEventListener('click', handleClickOutside);
    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  }, []);

  // 切换产品推荐下拉菜单的显示状态
  const toggleProductsDropdown = (e: React.MouseEvent) => {
    e.stopPropagation();
    setShowProductsDropdown(prev => !prev);
  };

  // 图标加载处理
  useEffect(() => {
    const timer = setTimeout(() => {
      setIconsLoaded(true);
    }, 200);
    
    return () => clearTimeout(timer);
  }, []);

  // 加载收藏工具和首次收藏状态
  useEffect(() => {
    const savedFavorites = localStorage.getItem('favoriteTools');
    const hasSeenFirstFavoriteNotification = localStorage.getItem('hasSeenFirstFavoriteNotification') === 'true';
    
    setFirstFavoriteAdded(hasSeenFirstFavoriteNotification);
    
    if (savedFavorites) {
      setFavoriteTools(JSON.parse(savedFavorites));
      setShowFavorites(true);
    }
  }, []);

  // 收藏/取消收藏工具
  const toggleFavorite = (toolCode: string, e: React.MouseEvent) => {
    e.stopPropagation();
    
    const isAdding = !favoriteTools.includes(toolCode);
    
    const updatedFavorites = isAdding
      ? [...favoriteTools, toolCode]
      : favoriteTools.filter(code => code !== toolCode);
    
    setFavoriteTools(updatedFavorites);
    localStorage.setItem('favoriteTools', JSON.stringify(updatedFavorites));
    
    setShowFavorites(updatedFavorites.length > 0);
    
    if (isAdding && !firstFavoriteAdded) {
      setShowNotification(true);
      setFirstFavoriteAdded(true);
      localStorage.setItem('hasSeenFirstFavoriteNotification', 'true');
      
      setTimeout(() => {
        setShowNotification(false);
      }, 5000);
    }
  };

  // 关闭通知
  const closeNotification = () => {
    setShowNotification(false);
  };

  // 切换到收藏分类
  const viewFavorites = () => {
    setActiveCategory('favorites');
  };

  // 预加载工具页面的函数
  const prefetchTool = (toolCode: string) => {
    if (prefetchedTools.has(toolCode)) {
      return;
    }
    
    const prefetchUrl = `/tools/${toolCode}`;
    fetch(prefetchUrl, { priority: 'low' })
      .then(response => {
        if (!response.ok) {
          throw new Error(`预加载失败: ${response.status}`);
        }
        return response.text();
      })
      .then(() => {
        setPrefetchedTools(prev => {
          const newSet = new Set(prev);
          newSet.add(toolCode);
          return newSet;
        });
      })
      .catch(error => {
        console.warn(`工具 ${toolCode} 预加载失败:`, error);
      });
  };

  // 处理工具导航
  const navigateToTool = (toolCode: string) => {
    localStorage.setItem('from_homepage', 'true');
    router.push(`/tools/${toolCode}`);
  };

  // 过滤工具列表
  const filteredTools = () => tools.filter(tool => {
    if (searchTerm && !t(`tools.${tool.code}.title`).toLowerCase().includes(searchTerm.toLowerCase()) && 
        !t(`tools.${tool.code}.description`).toLowerCase().includes(searchTerm.toLowerCase()) &&
        !tool.keywords?.some(keyword => keyword.toLowerCase().includes(searchTerm.toLowerCase()))) {
      return false;
    }
    
    if (activeCategory === 'favorites') {
      return favoriteTools.includes(tool.code);
    }
    
    if (activeCategory !== "all" && !tool.category.includes(activeCategory)) {
      return false;
    }
    
    return true;
  });

  // 构建分类列表
  const allCategories = [
    ...categories.slice(0, 2),
    ...(showFavorites ? [{ code: "favorites", name: t('common.favorites'), active: false }] : []),
    ...categories.slice(2)
  ];

  // 更新分类选择处理函数
  const handleCategoryChange = (categoryCode: string) => {
    setActiveCategory(categoryCode);
    localStorage.setItem('lastActiveCategory', categoryCode);
  };

  return (
    <div className={`min-h-screen bg-gray-50 dark:bg-gray-900 transition-colors duration-300 ${!iconsLoaded ? 'opacity-90' : 'opacity-100 transition-opacity duration-300'}`}>

      {/* 通知提示 */}
      {showNotification && (
        <div className="fixed top-4 left-1/2 transform -translate-x-1/2 z-50 flex items-center gap-3 bg-white dark:bg-gray-800 text-gray-900 dark:text-white px-4 py-3 rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 animate-fadeIn">
          <FontAwesomeIcon icon={faBell} className="text-blue-500" />
          <span>工具已添加到收藏夹，点击导航栏中的&quot;我的收藏&quot;查看</span>
          <button
            className="ml-2 text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200 transition-colors"
            onClick={closeNotification}
          >
            <FontAwesomeIcon icon={faTimes} />
          </button>
        </div>
      )}

      {/* 主容器 */}
      <div className="min-h-screen flex flex-col max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">

        {/* 简洁头部设计 */}
        <header className="py-8 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between mb-8">
            {/* Logo和标题 */}
            <div className="flex items-center space-x-4">
              <div className="w-10 h-10 bg-blue-500 rounded-lg flex items-center justify-center">
                <FontAwesomeIcon icon={faCube} className="text-white text-lg" />
              </div>
              <div>
                <h1 className="text-2xl font-bold text-gray-900 dark:text-white">
                  {t('common.siteName')}
                </h1>
                <p className="text-sm text-gray-600 dark:text-gray-400">{t('common.siteDesc')}</p>
              </div>
            </div>

            {/* 右侧按钮 */}
            <div className="flex items-center space-x-3">
              <button
                className="w-10 h-10 bg-yellow-500 hover:bg-yellow-600 rounded-lg flex items-center justify-center transition-colors"
                onClick={viewFavorites}
                title={t('common.favorites')}
              >
                <FontAwesomeIcon icon={faStar} className="text-white" />
              </button>
              <LanguageToggle />
              <ThemeToggle />
            </div>
          </div>

          {/* 简洁搜索框 */}
          <div className="relative max-w-2xl mx-auto">
            <div className="relative">
              <input
                type="text"
                placeholder={t('common.search')}
                className="w-full h-12 bg-white dark:bg-gray-800 rounded-lg pl-12 pr-12 text-gray-900 dark:text-white border border-gray-300 dark:border-gray-600 focus:border-blue-500 focus:ring-2 focus:ring-blue-500/20 outline-none transition-all placeholder:text-gray-500 dark:placeholder:text-gray-400"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />

              <div className="absolute left-4 top-1/2 transform -translate-y-1/2 text-gray-400">
                <FontAwesomeIcon icon={faSearch} />
              </div>

              {searchTerm && (
                <button
                  className="absolute right-4 top-1/2 transform -translate-y-1/2 w-6 h-6 rounded-full bg-gray-200 dark:bg-gray-600 hover:bg-gray-300 dark:hover:bg-gray-500 flex items-center justify-center transition-colors"
                  onClick={() => setSearchTerm('')}
                >
                  <FontAwesomeIcon icon={faTimes} className="text-gray-600 dark:text-gray-300 text-xs" />
                </button>
              )}
            </div>
          </div>
        </header>

        {/* 主内容区域 */}
        <main className="flex-1 py-8">
          {/* 简洁分类导航 */}
          <div className="mb-8">
            <div className="flex flex-wrap gap-2 justify-center">
              {allCategories.map((category, index) => (
                <button
                  key={index}
                  className={`px-4 py-2 rounded-lg text-sm font-medium transition-colors ${
                    activeCategory === category.code
                      ? 'bg-blue-500 text-white'
                      : 'bg-white dark:bg-gray-800 text-gray-700 dark:text-gray-300 border border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700'
                  }`}
                  onClick={() => handleCategoryChange(category.code)}
                >
                  {t(`categories.${category.code}`)}
                </button>
              ))}
            </div>
          </div>

          {/* 简洁工具卡片 */}
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {filteredTools().map((tool, index) => (
              <div
                key={index}
                className="group bg-white dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700 p-6 cursor-pointer hover:shadow-lg hover:border-blue-300 dark:hover:border-blue-600 transition-all duration-200"
                onClick={() => navigateToTool(tool.code)}
                onMouseEnter={() => prefetchTool(tool.code)}
              >
                <Link
                  href={`/tools/${tool.code}`}
                  prefetch={true}
                  className="hidden"
                  aria-hidden="true"
                />

                <div className="flex items-start justify-between mb-4">
                  <div className="w-12 h-12 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                    <FontAwesomeIcon icon={tool.icon} className="text-blue-600 dark:text-blue-400 text-lg" />
                  </div>

                  <button
                    className="w-8 h-8 rounded-lg bg-gray-100 dark:bg-gray-700 hover:bg-yellow-100 dark:hover:bg-yellow-900 flex items-center justify-center transition-colors"
                    onClick={(e) => toggleFavorite(tool.code, e)}
                  >
                    <FontAwesomeIcon
                      icon={favoriteTools.includes(tool.code) ? faStar : farStar}
                      className={`text-sm ${
                        favoriteTools.includes(tool.code)
                          ? 'text-yellow-500'
                          : 'text-gray-400 hover:text-yellow-500'
                      }`}
                    />
                  </button>
                </div>

                <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">
                  {t(`tools.${tool.code}.title`)}
                </h3>

                <div className="flex flex-wrap gap-1 mb-3">
                  {tool.category.slice(0, 2).map((catCode, catIndex) => (
                    <span
                      key={catIndex}
                      className="px-2 py-1 text-xs bg-gray-100 dark:bg-gray-700 text-gray-600 dark:text-gray-400 rounded"
                    >
                      {t(`categories.${catCode}`)}
                    </span>
                  ))}
                </div>

                <p className="text-sm text-gray-600 dark:text-gray-400 leading-relaxed">
                  {t(`tools.${tool.code}.description`)}
                </p>
              </div>
            ))}
          </div>
        </main>

        {/* 返回顶部按钮 */}
        <BackToTop
          scrollThreshold={400}
          position="bottom-right"
          offset={24}
          size="medium"
        />
      </div>
    </div>
  );
}
