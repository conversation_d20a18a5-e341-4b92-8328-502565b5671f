'use client';

import React, { useState, useEffect } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faArrowUp } from '@fortawesome/free-solid-svg-icons';

interface BackToTopProps {
  scrollThreshold?: number; // 显示按钮的滚动阈值（像素）
  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left'; // 按钮位置
  offset?: number; // 与屏幕边缘的距离（像素）
  containerRef?: React.RefObject<HTMLElement>; // 滚动容器引用，默认为window
  zIndex?: number; // 自定义z-index
  size?: 'small' | 'medium' | 'large'; // 按钮大小
}

/**
 * 回到顶部组件
 * 
 * 用法:
 * 1. 基本用法: <BackToTop />
 * 2. 自定义: <BackToTop position="bottom-left" offset={30} size="large" />
 * 3. 对特定容器: 
 *    const containerRef = useRef<HTMLDivElement>(null);
 *    <div ref={containerRef} style={{height: '500px', overflow: 'auto'}}>
 *      内容
 *      <BackToTop containerRef={containerRef} />
 *    </div>
 */
const BackToTop: React.FC<BackToTopProps> = ({
  scrollThreshold = 300,
  position = 'bottom-right',
  offset = 20,
  containerRef,
  zIndex = 40,
  size = 'medium'
}) => {
  const [isVisible, setIsVisible] = useState(false);

  // 生成位置样式
  const getPositionStyle = () => {
    const positionStyle: React.CSSProperties = {};
    
    if (position.includes('bottom')) {
      positionStyle.bottom = offset;
    } else {
      positionStyle.top = offset;
    }
    
    if (position.includes('right')) {
      positionStyle.right = offset;
    } else {
      positionStyle.left = offset;
    }
    
    return positionStyle;
  };

  // 根据大小获取样式类名
  const getSizeClassName = () => {
    switch (size) {
      case 'small':
        return 'w-8 h-8';
      case 'large':
        return 'w-12 h-12';
      case 'medium':
      default:
        return 'w-10 h-10';
    }
  };

  // 处理滚动事件
  const handleScroll = () => {
    if (containerRef && containerRef.current) {
      setIsVisible(containerRef.current.scrollTop > scrollThreshold);
    } else {
      setIsVisible(window.scrollY > scrollThreshold);
    }
  };

  // 回到顶部
  const scrollToTop = () => {
    if (containerRef && containerRef.current) {
      containerRef.current.scrollTo({
        top: 0,
        behavior: 'smooth'
      });
    } else {
      window.scrollTo({
        top: 0,
        behavior: 'smooth'
      });
    }
  };

  // 设置滚动监听
  useEffect(() => {
    const scrollElement = containerRef?.current || window;
    scrollElement.addEventListener('scroll', handleScroll);
    
    // 初始检查
    handleScroll();
    
    return () => {
      scrollElement.removeEventListener('scroll', handleScroll);
    };
  }, [containerRef, scrollThreshold]);

  // 如果不可见，不渲染
  if (!isVisible) return null;

  const positionStyle = getPositionStyle();

  return (
    <button
      onClick={scrollToTop}
      className={`
        fixed
        ${getSizeClassName()}
        bg-gradient-to-br from-purple-500 to-blue-600
        text-white
        rounded-2xl
        flex
        items-center
        justify-center
        shadow-2xl
        hover:shadow-[0_0_40px_rgba(147,51,234,0.6)]
        transition-all
        duration-500
        hover:scale-125
        hover:-translate-y-2
        hover:rotate-12
        focus:outline-none
        focus:ring-4
        focus:ring-purple-400/30
        backdrop-blur-xl
        border-2 border-white/20
        animate-fadeIn
        group
        overflow-hidden
      `}
      style={{
        ...positionStyle,
        zIndex
      }}
      aria-label="回到顶部"
      title="回到顶部"
    >
      {/* 背景装饰 */}
      <div className="absolute inset-0 bg-gradient-to-br from-purple-400/20 to-blue-400/20 animate-pulse"></div>

      {/* 箭头图标 */}
      <FontAwesomeIcon
        icon={faArrowUp}
        className="relative z-10 transition-all duration-300 group-hover:scale-125 group-hover:-translate-y-1"
      />

      {/* 外部发光效果 */}
      <div className="absolute -inset-2 bg-gradient-to-br from-purple-500/30 to-blue-500/30 rounded-2xl blur opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>

      {/* 点击波纹效果 */}
      <div className="absolute inset-0 rounded-2xl bg-white/20 scale-0 group-active:scale-100 transition-transform duration-200"></div>
    </button>
  );
};

export default BackToTop; 