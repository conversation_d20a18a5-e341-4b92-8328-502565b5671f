'use client';

import React, { useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faLanguage } from '@fortawesome/free-solid-svg-icons';
import { useLanguage } from '@/context/LanguageContext';
import { Language } from '@/config/i18n';

export default function LanguageToggle() {
  const { language, changeLanguage, t } = useLanguage();
  const [showDropdown, setShowDropdown] = useState(false);
  
  // 切换下拉菜单显示状态
  const toggleDropdown = () => {
    setShowDropdown(prev => !prev);
  };
  
  // 选择语言
  const selectLanguage = (lang: Language) => {
    changeLanguage(lang);
    setShowDropdown(false);
  };
  
  return (
    <div className="relative">
      <button
        className="w-10 h-10 bg-[rgba(var(--color-bg-secondary),0.8)] backdrop-blur-sm rounded-xl flex items-center justify-center group relative overflow-hidden border border-[rgba(var(--color-primary),0.2)] hover:border-[rgba(var(--color-primary),0.4)] transition-all duration-300 hover:scale-105 hover:shadow-lg"
        onClick={toggleDropdown}
        aria-label={t('common.language.title')}
        title={t('common.language.title')}
      >
        <div className="relative z-10">
          <FontAwesomeIcon
            icon={faLanguage}
            className="text-[rgb(var(--color-primary))] transition-all duration-300 group-hover:scale-110"
          />
        </div>

        {/* 悬停背景效果 */}
        <div className="absolute inset-0 bg-gradient-to-r from-[rgba(var(--color-primary),0.1)] to-[rgba(var(--color-primary-hover),0.1)] opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

        <span className="absolute left-1/2 -translate-x-1/2 top-full mt-3 px-3 py-2 bg-[rgba(var(--color-bg-card),0.95)] backdrop-blur-sm rounded-lg whitespace-nowrap opacity-0 group-hover:opacity-100 pointer-events-none transition-all duration-300 text-sm z-10 border border-[rgba(var(--color-primary),0.2)] shadow-lg"
          style={{
            color: 'rgb(var(--color-text-primary))'
          }}>
          {t('common.language.title')}
        </span>
      </button>

      {/* 语言选择下拉菜单 */}
      {showDropdown && (
        <div
          className="absolute right-0 top-full mt-3 py-2 w-36 bg-[rgba(var(--color-bg-card),0.95)] backdrop-blur-xl shadow-2xl rounded-xl border border-[rgba(var(--color-primary),0.2)] z-50 animate-fadeIn"
        >
          <button
            className={`w-full text-left px-4 py-3 hover:bg-[rgba(var(--color-primary),0.1)] transition-all duration-200 rounded-lg mx-1 ${
              language === 'zh'
                ? 'text-[rgb(var(--color-primary))] font-medium bg-[rgba(var(--color-primary),0.1)]'
                : 'text-[rgb(var(--color-text-primary))]'
            }`}
            onClick={() => selectLanguage('zh')}
          >
            {t('common.language.zh')}
          </button>
          <button
            className={`w-full text-left px-4 py-3 hover:bg-[rgba(var(--color-primary),0.1)] transition-all duration-200 rounded-lg mx-1 ${
              language === 'en'
                ? 'text-[rgb(var(--color-primary))] font-medium bg-[rgba(var(--color-primary),0.1)]'
                : 'text-[rgb(var(--color-text-primary))]'
            }`}
            onClick={() => selectLanguage('en')}
          >
            {t('common.language.en')}
          </button>
        </div>
      )}
    </div>
  );
} 