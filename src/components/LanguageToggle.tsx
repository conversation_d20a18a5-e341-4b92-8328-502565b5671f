'use client';

import React, { useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faLanguage } from '@fortawesome/free-solid-svg-icons';
import { useLanguage } from '@/context/LanguageContext';
import { Language } from '@/config/i18n';

export default function LanguageToggle() {
  const { language, changeLanguage, t } = useLanguage();
  const [showDropdown, setShowDropdown] = useState(false);
  
  // 切换下拉菜单显示状态
  const toggleDropdown = () => {
    setShowDropdown(prev => !prev);
  };
  
  // 选择语言
  const selectLanguage = (lang: Language) => {
    changeLanguage(lang);
    setShowDropdown(false);
  };
  
  return (
    <div className="relative">
      <button
        className="group relative w-12 h-12 bg-black/30 backdrop-blur-xl rounded-2xl flex items-center justify-center border border-white/20 hover:border-blue-400/50 transition-all duration-300 hover:scale-110 hover:shadow-2xl hover:shadow-blue-400/20"
        onClick={toggleDropdown}
        aria-label={t('common.language.title')}
        title={t('common.language.title')}
      >
        <div className="relative z-10">
          <FontAwesomeIcon
            icon={faLanguage}
            className="text-blue-400 text-lg transition-all duration-300 group-hover:scale-110 group-hover:rotate-12"
          />
        </div>

        {/* 发光效果 */}
        <div className="absolute -inset-1 bg-gradient-to-r from-blue-400/20 to-purple-400/20 rounded-2xl blur opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

        <span className="absolute left-1/2 -translate-x-1/2 top-full mt-3 px-3 py-2 bg-black/80 backdrop-blur-xl rounded-xl whitespace-nowrap opacity-0 group-hover:opacity-100 pointer-events-none transition-all duration-300 text-sm z-10 border border-white/20 shadow-2xl text-white">
          {t('common.language.title')}
        </span>
      </button>

      {/* 语言选择下拉菜单 */}
      {showDropdown && (
        <div
          className="absolute right-0 top-full mt-3 py-3 w-40 bg-black/80 backdrop-blur-2xl shadow-2xl rounded-2xl border border-white/20 z-50 animate-fadeIn"
        >
          <button
            className={`w-full text-left px-4 py-3 hover:bg-white/10 transition-all duration-200 rounded-xl mx-2 ${
              language === 'zh'
                ? 'text-blue-400 font-bold bg-white/10'
                : 'text-white hover:text-blue-400'
            }`}
            onClick={() => selectLanguage('zh')}
          >
            {t('common.language.zh')}
          </button>
          <button
            className={`w-full text-left px-4 py-3 hover:bg-white/10 transition-all duration-200 rounded-xl mx-2 ${
              language === 'en'
                ? 'text-blue-400 font-bold bg-white/10'
                : 'text-white hover:text-blue-400'
            }`}
            onClick={() => selectLanguage('en')}
          >
            {t('common.language.en')}
          </button>
        </div>
      )}
    </div>
  );
} 