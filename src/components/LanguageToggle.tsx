'use client';

import React, { useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faLanguage } from '@fortawesome/free-solid-svg-icons';
import { useLanguage } from '@/context/LanguageContext';
import { Language } from '@/config/i18n';

export default function LanguageToggle() {
  const { language, changeLanguage, t } = useLanguage();
  const [showDropdown, setShowDropdown] = useState(false);
  
  // 切换下拉菜单显示状态
  const toggleDropdown = () => {
    setShowDropdown(prev => !prev);
  };
  
  // 选择语言
  const selectLanguage = (lang: Language) => {
    changeLanguage(lang);
    setShowDropdown(false);
  };
  
  return (
    <div className="relative">
      <button
        className="w-10 h-10 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg flex items-center justify-center hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
        onClick={toggleDropdown}
        aria-label={t('common.language.title')}
        title={t('common.language.title')}
      >
        <FontAwesomeIcon
          icon={faLanguage}
          className="text-gray-600 dark:text-gray-400"
        />
      </button>

      {/* 语言选择下拉菜单 */}
      {showDropdown && (
        <div
          className="absolute right-0 top-full mt-2 py-2 w-32 bg-white dark:bg-gray-800 shadow-lg rounded-lg border border-gray-200 dark:border-gray-700 z-50 animate-fadeIn"
        >
          <button
            className={`w-full text-left px-4 py-2 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors ${
              language === 'zh'
                ? 'text-blue-600 dark:text-blue-400 font-medium'
                : 'text-gray-700 dark:text-gray-300'
            }`}
            onClick={() => selectLanguage('zh')}
          >
            {t('common.language.zh')}
          </button>
          <button
            className={`w-full text-left px-4 py-2 hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors ${
              language === 'en'
                ? 'text-blue-600 dark:text-blue-400 font-medium'
                : 'text-gray-700 dark:text-gray-300'
            }`}
            onClick={() => selectLanguage('en')}
          >
            {t('common.language.en')}
          </button>
        </div>
      )}
    </div>
  );
} 