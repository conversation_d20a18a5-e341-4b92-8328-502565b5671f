'use client';

import React, { useEffect, useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faLightbulb } from '@fortawesome/free-solid-svg-icons';
import { faLightbulb as farLightbulb } from '@fortawesome/free-regular-svg-icons';
import { useLanguage } from '@/context/LanguageContext';

export default function ThemeToggle() {
  const [theme, setTheme] = useState('dark');
  const [mounted, setMounted] = useState(false);
  const { t } = useLanguage();

  // 组件挂载后执行
  useEffect(() => {
    setMounted(true);
    const savedTheme = localStorage.getItem('theme');
    if (savedTheme) {
      setTheme(savedTheme);
      document.documentElement.setAttribute('data-theme', savedTheme);
    }
  }, []);

  // 切换主题
  const toggleTheme = () => {
    const newTheme = theme === 'dark' ? 'light' : 'dark';
    setTheme(newTheme);
    
    // 保存到本地存储并设置 data-theme 属性
    localStorage.setItem('theme', newTheme);
    document.documentElement.setAttribute('data-theme', newTheme);
  };

  // 如果组件尚未挂载，返回空白以避免服务器/客户端不匹配
  if (!mounted) return null;

  return (
    <button
      className="group relative w-12 h-12 bg-black/30 backdrop-blur-xl rounded-2xl flex items-center justify-center border border-white/20 hover:border-yellow-400/50 transition-all duration-300 hover:scale-110 hover:shadow-2xl hover:shadow-yellow-400/20"
      onClick={toggleTheme}
      aria-label={theme === 'dark' ? t('common.theme.light') : t('common.theme.dark')}
      title={theme === 'dark' ? t('common.theme.light') : t('common.theme.dark')}
    >
      <div className="relative z-10">
        {theme === 'dark' ? (
          // 灯泡图标 - 深色模式下显示(实心灯泡表示可以"点亮")
          <FontAwesomeIcon
            icon={faLightbulb}
            className="text-yellow-400 text-lg transition-all duration-300 group-hover:scale-110 group-hover:rotate-12"
          />
        ) : (
          // 灯泡轮廓图标 - 浅色模式下显示(空心灯泡表示可以"关闭")
          <FontAwesomeIcon
            icon={farLightbulb}
            className="text-purple-400 text-lg transition-all duration-300 group-hover:scale-110 group-hover:rotate-12"
          />
        )}
      </div>

      {/* 发光效果 */}
      <div className="absolute -inset-1 bg-gradient-to-r from-yellow-400/20 to-orange-400/20 rounded-2xl blur opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>

      <span className="absolute left-1/2 -translate-x-1/2 top-full mt-3 px-3 py-2 bg-black/80 backdrop-blur-xl rounded-xl whitespace-nowrap opacity-0 group-hover:opacity-100 pointer-events-none transition-all duration-300 text-sm z-10 border border-white/20 shadow-2xl text-white">
        {theme === 'dark' ? t('common.theme.light') : t('common.theme.dark')}
      </span>
    </button>
  );
} 