'use client';

import React, { useEffect, useState } from 'react';
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome';
import { faLightbulb } from '@fortawesome/free-solid-svg-icons';
import { faLightbulb as farLightbulb } from '@fortawesome/free-regular-svg-icons';
import { useLanguage } from '@/context/LanguageContext';

export default function ThemeToggle() {
  const [theme, setTheme] = useState('dark');
  const [mounted, setMounted] = useState(false);
  const { t } = useLanguage();

  // 组件挂载后执行
  useEffect(() => {
    setMounted(true);
    const savedTheme = localStorage.getItem('theme') || 'light';
    setTheme(savedTheme);

    // 设置 Tailwind 的 dark 类
    if (savedTheme === 'dark') {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  }, []);

  // 切换主题
  const toggleTheme = () => {
    const newTheme = theme === 'dark' ? 'light' : 'dark';
    setTheme(newTheme);

    // 保存到本地存储并设置 Tailwind 的 dark 类
    localStorage.setItem('theme', newTheme);
    if (newTheme === 'dark') {
      document.documentElement.classList.add('dark');
    } else {
      document.documentElement.classList.remove('dark');
    }
  };

  // 如果组件尚未挂载，返回空白以避免服务器/客户端不匹配
  if (!mounted) return null;

  return (
    <button
      className="w-10 h-10 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg flex items-center justify-center hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
      onClick={toggleTheme}
      aria-label={theme === 'dark' ? t('common.theme.light') : t('common.theme.dark')}
      title={theme === 'dark' ? t('common.theme.light') : t('common.theme.dark')}
    >
      {theme === 'dark' ? (
        <FontAwesomeIcon
          icon={faLightbulb}
          className="text-yellow-500"
        />
      ) : (
        <FontAwesomeIcon
          icon={farLightbulb}
          className="text-gray-600"
        />
      )}
    </button>
  );
} 