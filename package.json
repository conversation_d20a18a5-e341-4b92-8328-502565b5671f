{"name": "xiaoxia", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-brands-svg-icons": "^6.7.2", "@fortawesome/free-regular-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@types/crypto-js": "^4.2.2", "@types/file-saver": "^2.0.7", "@uiw/react-json-view": "^2.0.0-alpha.30", "compressorjs": "^1.2.1", "cron-parser": "^5.1.1", "cronstrue": "^2.59.0", "crypto-js": "^4.2.0", "fabric": "^6.6.4", "file-saver": "^2.0.5", "iconv-lite": "^0.6.3", "marked": "^15.0.7", "nanoid": "^5.1.5", "next": "15.2.3", "pdf-lib": "^1.17.1", "pdfjs-dist": "^5.4.54", "react": "^19.0.0", "react-dom": "^19.0.0", "react-pdf": "^10.0.1", "react-qrcode-logo": "^3.0.0", "turndown": "^7.2.0", "vanilla-jsoneditor": "^3.1.1"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/marked": "^5.0.2", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "@types/turndown": "^5.0.5", "autoprefixer": "^10.4.19", "eslint": "^9", "eslint-config-next": "15.2.3", "postcss": "^8.4.38", "tailwindcss": "^3.4.1", "typescript": "^5"}}