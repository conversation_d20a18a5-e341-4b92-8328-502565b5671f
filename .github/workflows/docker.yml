name: Docker CI

on:
  push:
    branches:
      - "master"
    tags:
      - 'v*'
  pull_request:
    branches:
      - "master"

jobs:
  docker:
    runs-on: ubuntu-latest
    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v2

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v2

      - name: Login to DockerHub
        if: github.event_name != 'pull_request'
        uses: docker/login-action@v2
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Extract metadata (tags, labels) for Docker
        id: meta
        uses: docker/metadata-action@v4
        with:
          images: star7th/xiaoxia
          tags: |
            type=ref,event=branch
            type=ref,event=pr
            type=semver,pattern={{version}}
            type=semver,pattern={{major}}.{{minor}}
            type=sha,format=short
            type=raw,value=latest,enable=${{ github.ref == 'refs/heads/master' }}

      - name: Build and push
        uses: docker/build-push-action@v4
        with:
          context: .
          push: ${{ github.event_name != 'pull_request' }}
          tags: ${{ steps.meta.outputs.tags }}
          labels: ${{ steps.meta.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max

      - name: Extract metadata for ARM Docker
        id: meta-arm
        if: github.ref == 'refs/heads/master' && github.event_name != 'pull_request'
        uses: docker/metadata-action@v4
        with:
          images: star7th/xiaoxia
          tags: |
            type=raw,value=arm-latest

      - name: Build and push ARM version
        if: github.ref == 'refs/heads/master' && github.event_name != 'pull_request'
        uses: docker/build-push-action@v4
        with:
          context: .
          file: ./Dockerfile-ARM
          push: true
          platforms: linux/arm64
          tags: ${{ steps.meta-arm.outputs.tags }}
          labels: ${{ steps.meta-arm.outputs.labels }}
          cache-from: type=gha
          cache-to: type=gha,mode=max 